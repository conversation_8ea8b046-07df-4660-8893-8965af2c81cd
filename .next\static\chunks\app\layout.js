/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(self["webpackChunk_N_E"] = self["webpackChunk_N_E"] || []).push([["app/layout"],{

/***/ "(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!":
/*!***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false! ***!
  \***********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ (function(__unused_webpack_module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(app-pages-browser)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/auth-context.tsx */ \"(app-pages-browser)/./lib/auth-context.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-jetbrains-mono\"}],\"variableName\":\"jetbrainsMono\"} */ \"(app-pages-browser)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-jetbrains-mono\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/globals.css */ \"(app-pages-browser)/./app/globals.css\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js":
/*!*********************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js ***!
  \*********************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("var __dirname = \"/\";\n(()=>{\"use strict\";var e={592:(e,r,t)=>{var n=t(722);var i=Object.create(null);var a=typeof document===\"undefined\";var o=Array.prototype.forEach;function debounce(e,r){var t=0;return function(){var n=this;var i=arguments;var a=function functionCall(){return e.apply(n,i)};clearTimeout(t);t=setTimeout(a,r)}}function noop(){}function getCurrentScriptUrl(e){var r=i[e];if(!r){if(document.currentScript){r=document.currentScript.src}else{var t=document.getElementsByTagName(\"script\");var a=t[t.length-1];if(a){r=a.src}}i[e]=r}return function(e){if(!r){return null}var t=r.split(/([^\\\\/]+)\\.js$/);var i=t&&t[1];if(!i){return[r.replace(\".js\",\".css\")]}if(!e){return[r.replace(\".js\",\".css\")]}return e.split(\",\").map((function(e){var t=new RegExp(\"\".concat(i,\"\\\\.js$\"),\"g\");return n(r.replace(t,\"\".concat(e.replace(/{fileName}/g,i),\".css\")))}))}}function updateCss(e,r){if(!r){if(!e.href){return}r=e.href.split(\"?\")[0]}if(!isUrlRequest(r)){return}if(e.isLoaded===false){return}if(!r||!(r.indexOf(\".css\")>-1)){return}e.visited=true;var t=e.cloneNode();t.isLoaded=false;t.addEventListener(\"load\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.addEventListener(\"error\",(function(){if(t.isLoaded){return}t.isLoaded=true;e.parentNode.removeChild(e)}));t.href=\"\".concat(r,\"?\").concat(Date.now());if(e.nextSibling){e.parentNode.insertBefore(t,e.nextSibling)}else{e.parentNode.appendChild(t)}}function getReloadUrl(e,r){var t;e=n(e,{stripWWW:false});r.some((function(n){if(e.indexOf(r)>-1){t=n}}));return t}function reloadStyle(e){if(!e){return false}var r=document.querySelectorAll(\"link\");var t=false;o.call(r,(function(r){if(!r.href){return}var n=getReloadUrl(r.href,e);if(!isUrlRequest(n)){return}if(r.visited===true){return}if(n){updateCss(r,n);t=true}}));return t}function reloadAll(){var e=document.querySelectorAll(\"link\");o.call(e,(function(e){if(e.visited===true){return}updateCss(e)}))}function isUrlRequest(e){if(!/^[a-zA-Z][a-zA-Z\\d+\\-.]*:/.test(e)){return false}return true}e.exports=function(e,r){if(a){console.log(\"no window.document found, will not HMR CSS\");return noop}var t=getCurrentScriptUrl(e);function update(){var e=t(r.filename);var n=reloadStyle(e);if(r.locals){console.log(\"[HMR] Detected local css modules. Reload all css\");reloadAll();return}if(n){console.log(\"[HMR] css reload %s\",e.join(\" \"))}else{console.log(\"[HMR] Reload all css\");reloadAll()}}return debounce(update,50)}},722:e=>{function normalizeUrl(e){return e.reduce((function(e,r){switch(r){case\"..\":e.pop();break;case\".\":break;default:e.push(r)}return e}),[]).join(\"/\")}e.exports=function(e){e=e.trim();if(/^data:/i.test(e)){return e}var r=e.indexOf(\"//\")!==-1?e.split(\"//\")[0]+\"//\":\"\";var t=e.replace(new RegExp(r,\"i\"),\"\").split(\"/\");var n=t[0].toLowerCase().replace(/\\.$/,\"\");t[0]=\"\";var i=normalizeUrl(t);return r+n+i}}};var r={};function __nccwpck_require__(t){var n=r[t];if(n!==undefined){return n.exports}var i=r[t]={exports:{}};var a=true;try{e[t](i,i.exports,__nccwpck_require__);a=false}finally{if(a)delete r[t]}return i.exports}if(typeof __nccwpck_require__!==\"undefined\")__nccwpck_require__.ab=__dirname+\"/\";var t=__nccwpck_require__(592);module.exports=t})();//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony default export */ __webpack_exports__[\"default\"] = (\"498a80948a32\");\nif (true) { module.hot.accept() }\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2FwcC9nbG9iYWxzLmNzcyIsIm1hcHBpbmdzIjoiO0FBQUEsK0RBQWUsY0FBYztBQUM3QixJQUFJLElBQVUsSUFBSSxpQkFBaUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vYXBwL2dsb2JhbHMuY3NzPzZlODkiXSwic291cmNlc0NvbnRlbnQiOlsiZXhwb3J0IGRlZmF1bHQgXCI0OThhODA5NDhhMzJcIlxuaWYgKG1vZHVsZS5ob3QpIHsgbW9kdWxlLmhvdC5hY2NlcHQoKSB9XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./app/globals.css\n"));

/***/ }),

/***/ "(app-pages-browser)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: function() { return /* binding */ ThemeProvider; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider(param) {\n    let { children, defaultTheme = \"light\", enableSystem = true, disableTransitionOnChange = false } = param;\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: defaultTheme,\n        enableSystem: enableSystem,\n        disableTransitionOnChange: disableTransitionOnChange,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n_c = ThemeProvider;\nvar _c;\n$RefreshReg$(_c, \"ThemeProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4IiwibWFwcGluZ3MiOiI7Ozs7Ozs7OztBQUU4QjtBQUNtQztBQUUxRCxTQUFTQyxjQUFjLEtBVTdCO1FBVjZCLEVBQzVCRSxRQUFRLEVBQ1JDLGVBQWUsT0FBTyxFQUN0QkMsZUFBZSxJQUFJLEVBQ25CQyw0QkFBNEIsS0FBSyxFQU1sQyxHQVY2QjtJQVc1QixxQkFDRSw4REFBQ0osc0RBQWtCQTtRQUNqQkssV0FBVTtRQUNWSCxjQUFjQTtRQUNkQyxjQUFjQTtRQUNkQywyQkFBMkJBO2tCQUUxQkg7Ozs7OztBQUdQO0tBckJnQkYiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vY29tcG9uZW50cy90aGVtZS1wcm92aWRlci50c3g/OTI4OSJdLCJzb3VyY2VzQ29udGVudCI6WyJcInVzZSBjbGllbnRcIlxuXG5pbXBvcnQgKiBhcyBSZWFjdCBmcm9tIFwicmVhY3RcIlxuaW1wb3J0IHsgVGhlbWVQcm92aWRlciBhcyBOZXh0VGhlbWVzUHJvdmlkZXIgfSBmcm9tIFwibmV4dC10aGVtZXNcIlxuXG5leHBvcnQgZnVuY3Rpb24gVGhlbWVQcm92aWRlcih7XG4gIGNoaWxkcmVuLFxuICBkZWZhdWx0VGhlbWUgPSBcImxpZ2h0XCIsXG4gIGVuYWJsZVN5c3RlbSA9IHRydWUsXG4gIGRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2UgPSBmYWxzZVxufToge1xuICBjaGlsZHJlbjogUmVhY3QuUmVhY3ROb2RlXG4gIGRlZmF1bHRUaGVtZT86IHN0cmluZ1xuICBlbmFibGVTeXN0ZW0/OiBib29sZWFuXG4gIGRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2U/OiBib29sZWFuXG59KSB7XG4gIHJldHVybiAoXG4gICAgPE5leHRUaGVtZXNQcm92aWRlclxuICAgICAgYXR0cmlidXRlPVwiY2xhc3NcIlxuICAgICAgZGVmYXVsdFRoZW1lPXtkZWZhdWx0VGhlbWV9XG4gICAgICBlbmFibGVTeXN0ZW09e2VuYWJsZVN5c3RlbX1cbiAgICAgIGRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2U9e2Rpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2V9XG4gICAgPlxuICAgICAge2NoaWxkcmVufVxuICAgIDwvTmV4dFRoZW1lc1Byb3ZpZGVyPlxuICApXG59XG4iXSwibmFtZXMiOlsiUmVhY3QiLCJUaGVtZVByb3ZpZGVyIiwiTmV4dFRoZW1lc1Byb3ZpZGVyIiwiY2hpbGRyZW4iLCJkZWZhdWx0VGhlbWUiLCJlbmFibGVTeXN0ZW0iLCJkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlIiwiYXR0cmlidXRlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./components/theme-provider.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/api-client.ts":
/*!***************************!*\
  !*** ./lib/api-client.ts ***!
  \***************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: function() { return /* binding */ apiClient; }\n/* harmony export */ });\n/**\n * API Client for DealVerse OS Backend\n */ const API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\nclass ApiClient {\n    setToken(token) {\n        this.token = token;\n        if (true) {\n            localStorage.setItem(\"access_token\", token);\n        }\n    }\n    clearToken() {\n        this.token = null;\n        if (true) {\n            localStorage.removeItem(\"access_token\");\n            localStorage.removeItem(\"refresh_token\");\n        }\n    }\n    async request(endpoint) {\n        let options = arguments.length > 1 && arguments[1] !== void 0 ? arguments[1] : {};\n        const url = \"\".concat(this.baseUrl).concat(endpoint);\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            ...options.headers || {}\n        };\n        if (this.token) {\n            headers.Authorization = \"Bearer \".concat(this.token);\n        }\n        try {\n            const response = await fetch(url, {\n                ...options,\n                headers\n            });\n            if (!response.ok) {\n                if (response.status === 401) {\n                    // Token expired, try to refresh\n                    const refreshed = await this.refreshToken();\n                    if (refreshed) {\n                        // Retry the request with new token\n                        headers.Authorization = \"Bearer \".concat(this.token);\n                        const retryResponse = await fetch(url, {\n                            ...options,\n                            headers\n                        });\n                        if (retryResponse.ok) {\n                            const data = await retryResponse.json();\n                            return {\n                                data\n                            };\n                        }\n                    }\n                    // Refresh failed, clear tokens and redirect to login\n                    this.clearToken();\n                    if (true) {\n                        window.location.href = \"/auth/login\";\n                    }\n                    throw new Error(\"Authentication failed\");\n                }\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(errorData.detail || errorData.message || \"HTTP \".concat(response.status));\n            }\n            const data = await response.json();\n            return {\n                data\n            };\n        } catch (error) {\n            console.error(\"API request failed:\", error);\n            // Check if it's a network error\n            if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n                return {\n                    error: \"Unable to connect to server. Please check your connection and try again.\"\n                };\n            }\n            return {\n                error: error instanceof Error ? error.message : \"Unknown error\"\n            };\n        }\n    }\n    async refreshToken() {\n        const refreshToken =  true ? localStorage.getItem(\"refresh_token\") : 0;\n        if (!refreshToken) return false;\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/auth/refresh\"), {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refresh_token: refreshToken\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                this.setToken(data.access_token);\n                if (true) {\n                    localStorage.setItem(\"refresh_token\", data.refresh_token);\n                }\n                return true;\n            }\n        } catch (error) {\n            console.error(\"Token refresh failed:\", error);\n        }\n        return false;\n    }\n    // Authentication endpoints\n    async login(email, password) {\n        return this.request(\"/auth/login/json\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                email,\n                password\n            })\n        });\n    }\n    async register(userData) {\n        return this.request(\"/auth/register\", {\n            method: \"POST\",\n            body: JSON.stringify(userData)\n        });\n    }\n    async logout() {\n        const result = await this.request(\"/auth/logout\", {\n            method: \"POST\"\n        });\n        this.clearToken();\n        return result;\n    }\n    // User endpoints\n    async getCurrentUser() {\n        return this.request(\"/users/me\");\n    }\n    async updateCurrentUser(userData) {\n        return this.request(\"/users/me\", {\n            method: \"PUT\",\n            body: JSON.stringify(userData)\n        });\n    }\n    async getUsers(params) {\n        const query = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.skip) query.append(\"skip\", params.skip.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(\"/users?\".concat(query.toString()));\n    }\n    // Deal endpoints\n    async getDeals(params) {\n        const query = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.skip) query.append(\"skip\", params.skip.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) query.append(\"limit\", params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.stage) query.append(\"stage\", params.stage);\n        if (params === null || params === void 0 ? void 0 : params.status) query.append(\"status\", params.status);\n        return this.request(\"/deals?\".concat(query.toString()));\n    }\n    async getDeal(id) {\n        return this.request(\"/deals/\".concat(id));\n    }\n    async createDeal(dealData) {\n        return this.request(\"/deals\", {\n            method: \"POST\",\n            body: JSON.stringify(dealData)\n        });\n    }\n    async updateDeal(id, dealData) {\n        return this.request(\"/deals/\".concat(id), {\n            method: \"PUT\",\n            body: JSON.stringify(dealData)\n        });\n    }\n    async deleteDeal(id) {\n        return this.request(\"/deals/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    async getDealsStats() {\n        return this.request(\"/deals/stats/summary\");\n    }\n    // Client endpoints\n    async getClients(params) {\n        const query = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.skip) query.append(\"skip\", params.skip.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) query.append(\"limit\", params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.client_type) query.append(\"client_type\", params.client_type);\n        if (params === null || params === void 0 ? void 0 : params.industry) query.append(\"industry\", params.industry);\n        if (params === null || params === void 0 ? void 0 : params.search) query.append(\"search\", params.search);\n        return this.request(\"/clients?\".concat(query.toString()));\n    }\n    async getClient(id) {\n        return this.request(\"/clients/\".concat(id));\n    }\n    async createClient(clientData) {\n        return this.request(\"/clients\", {\n            method: \"POST\",\n            body: JSON.stringify(clientData)\n        });\n    }\n    async updateClient(id, clientData) {\n        return this.request(\"/clients/\".concat(id), {\n            method: \"PUT\",\n            body: JSON.stringify(clientData)\n        });\n    }\n    async deleteClient(id) {\n        return this.request(\"/clients/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    // Analytics endpoints\n    async getDashboardAnalytics() {\n        return this.request(\"/analytics/dashboard\");\n    }\n    async getDealsPerformance() {\n        let days = arguments.length > 0 && arguments[0] !== void 0 ? arguments[0] : 30;\n        return this.request(\"/analytics/deals/performance?days=\".concat(days));\n    }\n    async getClientInsights() {\n        return this.request(\"/analytics/clients/insights\");\n    }\n    async getTeamProductivity() {\n        return this.request(\"/analytics/team/productivity\");\n    }\n    // Document endpoints\n    async getDocuments(params) {\n        const query = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.skip) query.append(\"skip\", params.skip.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) query.append(\"limit\", params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.document_type) query.append(\"document_type\", params.document_type);\n        if (params === null || params === void 0 ? void 0 : params.deal_id) query.append(\"deal_id\", params.deal_id);\n        if (params === null || params === void 0 ? void 0 : params.status) query.append(\"status\", params.status);\n        return this.request(\"/documents?\".concat(query.toString()));\n    }\n    async getDocument(id) {\n        return this.request(\"/documents/\".concat(id));\n    }\n    async createDocument(documentData) {\n        return this.request(\"/documents\", {\n            method: \"POST\",\n            body: JSON.stringify(documentData)\n        });\n    }\n    async uploadDocument(file, metadata) {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        formData.append(\"title\", metadata.title);\n        if (metadata.document_type) formData.append(\"document_type\", metadata.document_type);\n        if (metadata.deal_id) formData.append(\"deal_id\", metadata.deal_id);\n        if (metadata.client_id) formData.append(\"client_id\", metadata.client_id);\n        if (metadata.is_confidential !== undefined) formData.append(\"is_confidential\", metadata.is_confidential.toString());\n        const headers = {};\n        if (this.token) {\n            headers.Authorization = \"Bearer \".concat(this.token);\n        }\n        try {\n            const response = await fetch(\"\".concat(this.baseUrl, \"/documents/upload\"), {\n                method: \"POST\",\n                headers,\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(errorData.detail || errorData.message || \"HTTP \".concat(response.status));\n            }\n            const data = await response.json();\n            return {\n                data\n            };\n        } catch (error) {\n            console.error(\"Document upload failed:\", error);\n            return {\n                error: error instanceof Error ? error.message : \"Unknown error\"\n            };\n        }\n    }\n    async updateDocument(id, documentData) {\n        return this.request(\"/documents/\".concat(id), {\n            method: \"PUT\",\n            body: JSON.stringify(documentData)\n        });\n    }\n    async deleteDocument(id) {\n        return this.request(\"/documents/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    // Document Analysis endpoints\n    async analyzeDocument(id, analysisType, focusAreas) {\n        return this.request(\"/documents/\".concat(id, \"/analyze\"), {\n            method: \"POST\",\n            body: JSON.stringify({\n                analysis_type: analysisType || \"full\",\n                focus_areas: focusAreas || [\n                    \"financial\",\n                    \"legal\",\n                    \"risk\"\n                ]\n            })\n        });\n    }\n    async getDocumentAnalysis(id) {\n        return this.request(\"/documents/\".concat(id, \"/analysis\"));\n    }\n    async assessDocumentRisk(documentIds, assessmentType) {\n        return this.request(\"/documents/risk-assessment\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                document_ids: documentIds,\n                assessment_type: assessmentType || \"comprehensive\"\n            })\n        });\n    }\n    async getRiskAssessments(params) {\n        const query = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.skip) query.append(\"skip\", params.skip.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(\"/documents/risk-assessments?\".concat(query));\n    }\n    async categorizeDocument(id) {\n        return this.request(\"/documents/\".concat(id, \"/categorize\"));\n    }\n    async getDocumentAnalytics() {\n        return this.request(\"/documents/analytics/statistics\");\n    }\n    async getHighRiskDocuments(params) {\n        const query = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.skip) query.append(\"skip\", params.skip.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) query.append(\"limit\", params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.risk_threshold) query.append(\"risk_threshold\", params.risk_threshold.toString());\n        return this.request(\"/documents/high-risk?\".concat(query));\n    }\n    // Financial Models endpoints\n    async getFinancialModels(params) {\n        const query = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.skip) query.append(\"skip\", params.skip.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) query.append(\"limit\", params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.model_type) query.append(\"model_type\", params.model_type);\n        if (params === null || params === void 0 ? void 0 : params.deal_id) query.append(\"deal_id\", params.deal_id);\n        if (params === null || params === void 0 ? void 0 : params.status) query.append(\"status\", params.status);\n        return this.request(\"/financial-models?\".concat(query.toString()));\n    }\n    async getFinancialModel(id) {\n        return this.request(\"/financial-models/\".concat(id));\n    }\n    async createFinancialModel(modelData) {\n        return this.request(\"/financial-models\", {\n            method: \"POST\",\n            body: JSON.stringify(modelData)\n        });\n    }\n    async updateFinancialModel(id, modelData) {\n        return this.request(\"/financial-models/\".concat(id), {\n            method: \"PUT\",\n            body: JSON.stringify(modelData)\n        });\n    }\n    async deleteFinancialModel(id) {\n        return this.request(\"/financial-models/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    async createModelVersion(id, versionData) {\n        return this.request(\"/financial-models/\".concat(id, \"/versions\"), {\n            method: \"POST\",\n            body: JSON.stringify(versionData)\n        });\n    }\n    async getModelVersions(id) {\n        return this.request(\"/financial-models/\".concat(id, \"/versions\"));\n    }\n    async getModelStatistics() {\n        return this.request(\"/financial-models/statistics\");\n    }\n    // Organization endpoints\n    async getCurrentOrganization() {\n        return this.request(\"/organizations/me\");\n    }\n    async updateCurrentOrganization(orgData) {\n        return this.request(\"/organizations/me\", {\n            method: \"PUT\",\n            body: JSON.stringify(orgData)\n        });\n    }\n    async getOrganizationStats() {\n        return this.request(\"/organizations/me/stats\");\n    }\n    // Presentation endpoints\n    async getPresentations(params) {\n        const query = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.skip) query.append(\"skip\", params.skip.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) query.append(\"limit\", params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.status) query.append(\"status\", params.status);\n        if (params === null || params === void 0 ? void 0 : params.presentation_type) query.append(\"presentation_type\", params.presentation_type);\n        if (params === null || params === void 0 ? void 0 : params.deal_id) query.append(\"deal_id\", params.deal_id);\n        if (params === null || params === void 0 ? void 0 : params.created_by_me) query.append(\"created_by_me\", params.created_by_me.toString());\n        return this.request(\"/presentations/?\".concat(query.toString()));\n    }\n    async getPresentation(id) {\n        return this.request(\"/presentations/\".concat(id));\n    }\n    async createPresentation(presentationData) {\n        return this.request(\"/presentations/\", {\n            method: \"POST\",\n            body: JSON.stringify(presentationData)\n        });\n    }\n    async updatePresentation(id, presentationData) {\n        return this.request(\"/presentations/\".concat(id), {\n            method: \"PUT\",\n            body: JSON.stringify(presentationData)\n        });\n    }\n    async deletePresentation(id) {\n        return this.request(\"/presentations/\".concat(id), {\n            method: \"DELETE\"\n        });\n    }\n    async createPresentationVersion(id) {\n        return this.request(\"/presentations/\".concat(id, \"/version\"), {\n            method: \"POST\"\n        });\n    }\n    async getPresentationsByDeal(dealId, params) {\n        const query = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.skip) query.append(\"skip\", params.skip.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(\"/presentations/deals/\".concat(dealId, \"?\").concat(query.toString()));\n    }\n    async getSharedPresentations(params) {\n        const query = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.skip) query.append(\"skip\", params.skip.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(\"/presentations/shared?\".concat(query.toString()));\n    }\n    // Slide endpoints\n    async getPresentationSlides(presentationId, params) {\n        const query = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.skip) query.append(\"skip\", params.skip.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(\"/presentations/\".concat(presentationId, \"/slides?\").concat(query.toString()));\n    }\n    async createSlide(presentationId, slideData) {\n        return this.request(\"/presentations/\".concat(presentationId, \"/slides/\"), {\n            method: \"POST\",\n            body: JSON.stringify(slideData)\n        });\n    }\n    async getSlide(presentationId, slideId) {\n        return this.request(\"/presentations/\".concat(presentationId, \"/slides/\").concat(slideId));\n    }\n    async updateSlide(presentationId, slideId, slideData) {\n        return this.request(\"/presentations/\".concat(presentationId, \"/slides/\").concat(slideId), {\n            method: \"PUT\",\n            body: JSON.stringify(slideData)\n        });\n    }\n    async deleteSlide(presentationId, slideId) {\n        return this.request(\"/presentations/\".concat(presentationId, \"/slides/\").concat(slideId), {\n            method: \"DELETE\"\n        });\n    }\n    async duplicateSlide(presentationId, slideId, newSlideNumber) {\n        return this.request(\"/presentations/\".concat(presentationId, \"/slides/\").concat(slideId, \"/duplicate?new_slide_number=\").concat(newSlideNumber), {\n            method: \"POST\"\n        });\n    }\n    // Template endpoints\n    async getPresentationTemplates(params) {\n        const query = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.skip) query.append(\"skip\", params.skip.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) query.append(\"limit\", params.limit.toString());\n        if (params === null || params === void 0 ? void 0 : params.category) query.append(\"category\", params.category);\n        if (params === null || params === void 0 ? void 0 : params.featured_only) query.append(\"featured_only\", params.featured_only.toString());\n        if (params === null || params === void 0 ? void 0 : params.public_only) query.append(\"public_only\", params.public_only.toString());\n        return this.request(\"/presentations/templates/?\".concat(query.toString()));\n    }\n    async createPresentationTemplate(templateData) {\n        return this.request(\"/presentations/templates/\", {\n            method: \"POST\",\n            body: JSON.stringify(templateData)\n        });\n    }\n    async getPresentationTemplate(id) {\n        return this.request(\"/presentations/templates/\".concat(id));\n    }\n    async createPresentationFromTemplate(templateId, presentationData) {\n        return this.request(\"/presentations/templates/\".concat(templateId, \"/use\"), {\n            method: \"POST\",\n            body: JSON.stringify(presentationData)\n        });\n    }\n    // Comment endpoints\n    async getPresentationComments(presentationId, params) {\n        const query = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.skip) query.append(\"skip\", params.skip.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) query.append(\"limit\", params.limit.toString());\n        if ((params === null || params === void 0 ? void 0 : params.resolved_only) !== undefined) query.append(\"resolved_only\", params.resolved_only.toString());\n        return this.request(\"/presentations/\".concat(presentationId, \"/comments?\").concat(query.toString()));\n    }\n    async createPresentationComment(presentationId, commentData) {\n        return this.request(\"/presentations/\".concat(presentationId, \"/comments/\"), {\n            method: \"POST\",\n            body: JSON.stringify(commentData)\n        });\n    }\n    async resolvePresentationComment(presentationId, commentId) {\n        return this.request(\"/presentations/\".concat(presentationId, \"/comments/\").concat(commentId, \"/resolve\"), {\n            method: \"PUT\"\n        });\n    }\n    // Collaboration endpoints\n    async getPresentationActivities(presentationId, params) {\n        const query = new URLSearchParams();\n        if (params === null || params === void 0 ? void 0 : params.skip) query.append(\"skip\", params.skip.toString());\n        if (params === null || params === void 0 ? void 0 : params.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(\"/presentations/\".concat(presentationId, \"/activities?\").concat(query.toString()));\n    }\n    async getPresentationActiveUsers(presentationId) {\n        return this.request(\"/presentations/\".concat(presentationId, \"/active-users\"));\n    }\n    constructor(baseUrl = API_BASE_URL){\n        this.token = null;\n        this.baseUrl = baseUrl;\n        // Get token from localStorage if available\n        if (true) {\n            this.token = localStorage.getItem(\"access_token\");\n        }\n    }\n}\n// Create singleton instance\nconst apiClient = new ApiClient();\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL2xpYi9hcGktY2xpZW50LnRzIiwibWFwcGluZ3MiOiI7Ozs7QUFBQTs7Q0FFQyxHQUVELE1BQU1BLGVBQWVDLDhCQUErQixJQUFJO0FBUXhELE1BQU1HO0lBYUpDLFNBQVNDLEtBQWEsRUFBRTtRQUN0QixJQUFJLENBQUNBLEtBQUssR0FBR0E7UUFDYixJQUFJLElBQWtCLEVBQWE7WUFDakNDLGFBQWFDLE9BQU8sQ0FBQyxnQkFBZ0JGO1FBQ3ZDO0lBQ0Y7SUFFQUcsYUFBYTtRQUNYLElBQUksQ0FBQ0gsS0FBSyxHQUFHO1FBQ2IsSUFBSSxJQUFrQixFQUFhO1lBQ2pDQyxhQUFhRyxVQUFVLENBQUM7WUFDeEJILGFBQWFHLFVBQVUsQ0FBQztRQUMxQjtJQUNGO0lBRUEsTUFBY0MsUUFDWkMsUUFBZ0IsRUFFUztZQUR6QkMsVUFBQUEsaUVBQXVCLENBQUM7UUFFeEIsTUFBTUMsTUFBTSxHQUFrQkYsT0FBZixJQUFJLENBQUNHLE9BQU8sRUFBWSxPQUFUSDtRQUU5QixNQUFNSSxVQUFrQztZQUN0QyxnQkFBZ0I7WUFDaEIsR0FBSUgsUUFBUUcsT0FBTyxJQUE4QixDQUFDLENBQUM7UUFDckQ7UUFFQSxJQUFJLElBQUksQ0FBQ1YsS0FBSyxFQUFFO1lBQ2RVLFFBQVFDLGFBQWEsR0FBRyxVQUFxQixPQUFYLElBQUksQ0FBQ1gsS0FBSztRQUM5QztRQUlBLElBQUk7WUFDRixNQUFNWSxXQUFXLE1BQU1DLE1BQU1MLEtBQUs7Z0JBQ2hDLEdBQUdELE9BQU87Z0JBQ1ZHO1lBQ0Y7WUFFQSxJQUFJLENBQUNFLFNBQVNFLEVBQUUsRUFBRTtnQkFDaEIsSUFBSUYsU0FBU0csTUFBTSxLQUFLLEtBQUs7b0JBQzNCLGdDQUFnQztvQkFDaEMsTUFBTUMsWUFBWSxNQUFNLElBQUksQ0FBQ0MsWUFBWTtvQkFDekMsSUFBSUQsV0FBVzt3QkFDYixtQ0FBbUM7d0JBQ25DTixRQUFRQyxhQUFhLEdBQUcsVUFBcUIsT0FBWCxJQUFJLENBQUNYLEtBQUs7d0JBQzVDLE1BQU1rQixnQkFBZ0IsTUFBTUwsTUFBTUwsS0FBSzs0QkFDckMsR0FBR0QsT0FBTzs0QkFDVkc7d0JBQ0Y7d0JBRUEsSUFBSVEsY0FBY0osRUFBRSxFQUFFOzRCQUNwQixNQUFNSyxPQUFPLE1BQU1ELGNBQWNFLElBQUk7NEJBQ3JDLE9BQU87Z0NBQUVEOzRCQUFLO3dCQUNoQjtvQkFDRjtvQkFFQSxxREFBcUQ7b0JBQ3JELElBQUksQ0FBQ2hCLFVBQVU7b0JBQ2YsSUFBSSxJQUFrQixFQUFhO3dCQUNqQ2tCLE9BQU9DLFFBQVEsQ0FBQ0MsSUFBSSxHQUFHO29CQUN6QjtvQkFDQSxNQUFNLElBQUlDLE1BQU07Z0JBQ2xCO2dCQUVBLE1BQU1DLFlBQVksTUFBTWIsU0FBU1EsSUFBSSxHQUFHTSxLQUFLLENBQUMsSUFBTyxFQUFDO2dCQUN0RCxNQUFNLElBQUlGLE1BQU1DLFVBQVVFLE1BQU0sSUFBSUYsVUFBVUcsT0FBTyxJQUFJLFFBQXdCLE9BQWhCaEIsU0FBU0csTUFBTTtZQUNsRjtZQUVBLE1BQU1JLE9BQU8sTUFBTVAsU0FBU1EsSUFBSTtZQUNoQyxPQUFPO2dCQUFFRDtZQUFLO1FBQ2hCLEVBQUUsT0FBT1UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsdUJBQXVCQTtZQUNyQyxnQ0FBZ0M7WUFDaEMsSUFBSUEsaUJBQWlCRSxhQUFhRixNQUFNRCxPQUFPLENBQUNJLFFBQVEsQ0FBQyxVQUFVO2dCQUNqRSxPQUFPO29CQUFFSCxPQUFPO2dCQUEyRTtZQUM3RjtZQUNBLE9BQU87Z0JBQUVBLE9BQU9BLGlCQUFpQkwsUUFBUUssTUFBTUQsT0FBTyxHQUFHO1lBQWdCO1FBQzNFO0lBQ0Y7SUFFQSxNQUFjWCxlQUFpQztRQUM3QyxNQUFNQSxlQUFlLEtBQWtCLEdBQ25DaEIsYUFBYWdDLE9BQU8sQ0FBQyxtQkFDckIsQ0FBSTtRQUVSLElBQUksQ0FBQ2hCLGNBQWMsT0FBTztRQUUxQixJQUFJO1lBQ0YsTUFBTUwsV0FBVyxNQUFNQyxNQUFNLEdBQWdCLE9BQWIsSUFBSSxDQUFDSixPQUFPLEVBQUMsa0JBQWdCO2dCQUMzRHlCLFFBQVE7Z0JBQ1J4QixTQUFTO29CQUFFLGdCQUFnQjtnQkFBbUI7Z0JBQzlDeUIsTUFBTUMsS0FBS0MsU0FBUyxDQUFDO29CQUFFQyxlQUFlckI7Z0JBQWE7WUFDckQ7WUFFQSxJQUFJTCxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2YsTUFBTUssT0FBTyxNQUFNUCxTQUFTUSxJQUFJO2dCQUNoQyxJQUFJLENBQUNyQixRQUFRLENBQUNvQixLQUFLb0IsWUFBWTtnQkFDL0IsSUFBSSxJQUFrQixFQUFhO29CQUNqQ3RDLGFBQWFDLE9BQU8sQ0FBQyxpQkFBaUJpQixLQUFLbUIsYUFBYTtnQkFDMUQ7Z0JBQ0EsT0FBTztZQUNUO1FBQ0YsRUFBRSxPQUFPVCxPQUFPO1lBQ2RDLFFBQVFELEtBQUssQ0FBQyx5QkFBeUJBO1FBQ3pDO1FBRUEsT0FBTztJQUNUO0lBRUEsMkJBQTJCO0lBQzNCLE1BQU1XLE1BQU1DLEtBQWEsRUFBRUMsUUFBZ0IsRUFBRTtRQUMzQyxPQUFPLElBQUksQ0FBQ3JDLE9BQU8sQ0FJaEIsb0JBQW9CO1lBQ3JCNkIsUUFBUTtZQUNSQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7Z0JBQUVJO2dCQUFPQztZQUFTO1FBQ3pDO0lBQ0Y7SUFFQSxNQUFNQyxTQUFTQyxRQU1kLEVBQUU7UUFDRCxPQUFPLElBQUksQ0FBQ3ZDLE9BQU8sQ0FBQyxrQkFBa0I7WUFDcEM2QixRQUFRO1lBQ1JDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ087UUFDdkI7SUFDRjtJQUVBLE1BQU1DLFNBQVM7UUFDYixNQUFNQyxTQUFTLE1BQU0sSUFBSSxDQUFDekMsT0FBTyxDQUFDLGdCQUFnQjtZQUFFNkIsUUFBUTtRQUFPO1FBQ25FLElBQUksQ0FBQy9CLFVBQVU7UUFDZixPQUFPMkM7SUFDVDtJQUVBLGlCQUFpQjtJQUNqQixNQUFNQyxpQkFBaUI7UUFDckIsT0FBTyxJQUFJLENBQUMxQyxPQUFPLENBQUM7SUFDdEI7SUFFQSxNQUFNMkMsa0JBQWtCSixRQUFhLEVBQUU7UUFDckMsT0FBTyxJQUFJLENBQUN2QyxPQUFPLENBQUMsYUFBYTtZQUMvQjZCLFFBQVE7WUFDUkMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDTztRQUN2QjtJQUNGO0lBRUEsTUFBTUssU0FBU0MsTUFBMEMsRUFBRTtRQUN6RCxNQUFNQyxRQUFRLElBQUlDO1FBQ2xCLElBQUlGLG1CQUFBQSw2QkFBQUEsT0FBUUcsSUFBSSxFQUFFRixNQUFNRyxNQUFNLENBQUMsUUFBUUosT0FBT0csSUFBSSxDQUFDRSxRQUFRO1FBQzNELElBQUlMLG1CQUFBQSw2QkFBQUEsT0FBUU0sS0FBSyxFQUFFTCxNQUFNRyxNQUFNLENBQUMsU0FBU0osT0FBT00sS0FBSyxDQUFDRCxRQUFRO1FBRTlELE9BQU8sSUFBSSxDQUFDbEQsT0FBTyxDQUFDLFVBQTJCLE9BQWpCOEMsTUFBTUksUUFBUTtJQUM5QztJQUVBLGlCQUFpQjtJQUNqQixNQUFNRSxTQUFTUCxNQUtkLEVBQUU7UUFDRCxNQUFNQyxRQUFRLElBQUlDO1FBQ2xCLElBQUlGLG1CQUFBQSw2QkFBQUEsT0FBUUcsSUFBSSxFQUFFRixNQUFNRyxNQUFNLENBQUMsUUFBUUosT0FBT0csSUFBSSxDQUFDRSxRQUFRO1FBQzNELElBQUlMLG1CQUFBQSw2QkFBQUEsT0FBUU0sS0FBSyxFQUFFTCxNQUFNRyxNQUFNLENBQUMsU0FBU0osT0FBT00sS0FBSyxDQUFDRCxRQUFRO1FBQzlELElBQUlMLG1CQUFBQSw2QkFBQUEsT0FBUVEsS0FBSyxFQUFFUCxNQUFNRyxNQUFNLENBQUMsU0FBU0osT0FBT1EsS0FBSztRQUNyRCxJQUFJUixtQkFBQUEsNkJBQUFBLE9BQVFuQyxNQUFNLEVBQUVvQyxNQUFNRyxNQUFNLENBQUMsVUFBVUosT0FBT25DLE1BQU07UUFFeEQsT0FBTyxJQUFJLENBQUNWLE9BQU8sQ0FBQyxVQUEyQixPQUFqQjhDLE1BQU1JLFFBQVE7SUFDOUM7SUFFQSxNQUFNSSxRQUFRQyxFQUFVLEVBQUU7UUFDeEIsT0FBTyxJQUFJLENBQUN2RCxPQUFPLENBQUMsVUFBYSxPQUFIdUQ7SUFDaEM7SUFFQSxNQUFNQyxXQUFXQyxRQUFhLEVBQUU7UUFDOUIsT0FBTyxJQUFJLENBQUN6RCxPQUFPLENBQUMsVUFBVTtZQUM1QjZCLFFBQVE7WUFDUkMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDeUI7UUFDdkI7SUFDRjtJQUVBLE1BQU1DLFdBQVdILEVBQVUsRUFBRUUsUUFBYSxFQUFFO1FBQzFDLE9BQU8sSUFBSSxDQUFDekQsT0FBTyxDQUFDLFVBQWEsT0FBSHVELEtBQU07WUFDbEMxQixRQUFRO1lBQ1JDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ3lCO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNRSxXQUFXSixFQUFVLEVBQUU7UUFDM0IsT0FBTyxJQUFJLENBQUN2RCxPQUFPLENBQUMsVUFBYSxPQUFIdUQsS0FBTTtZQUFFMUIsUUFBUTtRQUFTO0lBQ3pEO0lBRUEsTUFBTStCLGdCQUFnQjtRQUNwQixPQUFPLElBQUksQ0FBQzVELE9BQU8sQ0FBQztJQUN0QjtJQUVBLG1CQUFtQjtJQUNuQixNQUFNNkQsV0FBV2hCLE1BTWhCLEVBQUU7UUFDRCxNQUFNQyxRQUFRLElBQUlDO1FBQ2xCLElBQUlGLG1CQUFBQSw2QkFBQUEsT0FBUUcsSUFBSSxFQUFFRixNQUFNRyxNQUFNLENBQUMsUUFBUUosT0FBT0csSUFBSSxDQUFDRSxRQUFRO1FBQzNELElBQUlMLG1CQUFBQSw2QkFBQUEsT0FBUU0sS0FBSyxFQUFFTCxNQUFNRyxNQUFNLENBQUMsU0FBU0osT0FBT00sS0FBSyxDQUFDRCxRQUFRO1FBQzlELElBQUlMLG1CQUFBQSw2QkFBQUEsT0FBUWlCLFdBQVcsRUFBRWhCLE1BQU1HLE1BQU0sQ0FBQyxlQUFlSixPQUFPaUIsV0FBVztRQUN2RSxJQUFJakIsbUJBQUFBLDZCQUFBQSxPQUFRa0IsUUFBUSxFQUFFakIsTUFBTUcsTUFBTSxDQUFDLFlBQVlKLE9BQU9rQixRQUFRO1FBQzlELElBQUlsQixtQkFBQUEsNkJBQUFBLE9BQVFtQixNQUFNLEVBQUVsQixNQUFNRyxNQUFNLENBQUMsVUFBVUosT0FBT21CLE1BQU07UUFFeEQsT0FBTyxJQUFJLENBQUNoRSxPQUFPLENBQUMsWUFBNkIsT0FBakI4QyxNQUFNSSxRQUFRO0lBQ2hEO0lBRUEsTUFBTWUsVUFBVVYsRUFBVSxFQUFFO1FBQzFCLE9BQU8sSUFBSSxDQUFDdkQsT0FBTyxDQUFDLFlBQWUsT0FBSHVEO0lBQ2xDO0lBRUEsTUFBTVcsYUFBYUMsVUFBZSxFQUFFO1FBQ2xDLE9BQU8sSUFBSSxDQUFDbkUsT0FBTyxDQUFDLFlBQVk7WUFDOUI2QixRQUFRO1lBQ1JDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ21DO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNQyxhQUFhYixFQUFVLEVBQUVZLFVBQWUsRUFBRTtRQUM5QyxPQUFPLElBQUksQ0FBQ25FLE9BQU8sQ0FBQyxZQUFlLE9BQUh1RCxLQUFNO1lBQ3BDMUIsUUFBUTtZQUNSQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNtQztRQUN2QjtJQUNGO0lBRUEsTUFBTUUsYUFBYWQsRUFBVSxFQUFFO1FBQzdCLE9BQU8sSUFBSSxDQUFDdkQsT0FBTyxDQUFDLFlBQWUsT0FBSHVELEtBQU07WUFBRTFCLFFBQVE7UUFBUztJQUMzRDtJQUVBLHNCQUFzQjtJQUN0QixNQUFNeUMsd0JBQXdCO1FBQzVCLE9BQU8sSUFBSSxDQUFDdEUsT0FBTyxDQUFDO0lBQ3RCO0lBRUEsTUFBTXVFLHNCQUF1QztZQUFuQkMsT0FBQUEsaUVBQWU7UUFDdkMsT0FBTyxJQUFJLENBQUN4RSxPQUFPLENBQUMscUNBQTBDLE9BQUx3RTtJQUMzRDtJQUVBLE1BQU1DLG9CQUFvQjtRQUN4QixPQUFPLElBQUksQ0FBQ3pFLE9BQU8sQ0FBQztJQUN0QjtJQUVBLE1BQU0wRSxzQkFBc0I7UUFDMUIsT0FBTyxJQUFJLENBQUMxRSxPQUFPLENBQUM7SUFDdEI7SUFFQSxxQkFBcUI7SUFDckIsTUFBTTJFLGFBQWE5QixNQU1sQixFQUFFO1FBQ0QsTUFBTUMsUUFBUSxJQUFJQztRQUNsQixJQUFJRixtQkFBQUEsNkJBQUFBLE9BQVFHLElBQUksRUFBRUYsTUFBTUcsTUFBTSxDQUFDLFFBQVFKLE9BQU9HLElBQUksQ0FBQ0UsUUFBUTtRQUMzRCxJQUFJTCxtQkFBQUEsNkJBQUFBLE9BQVFNLEtBQUssRUFBRUwsTUFBTUcsTUFBTSxDQUFDLFNBQVNKLE9BQU9NLEtBQUssQ0FBQ0QsUUFBUTtRQUM5RCxJQUFJTCxtQkFBQUEsNkJBQUFBLE9BQVErQixhQUFhLEVBQUU5QixNQUFNRyxNQUFNLENBQUMsaUJBQWlCSixPQUFPK0IsYUFBYTtRQUM3RSxJQUFJL0IsbUJBQUFBLDZCQUFBQSxPQUFRZ0MsT0FBTyxFQUFFL0IsTUFBTUcsTUFBTSxDQUFDLFdBQVdKLE9BQU9nQyxPQUFPO1FBQzNELElBQUloQyxtQkFBQUEsNkJBQUFBLE9BQVFuQyxNQUFNLEVBQUVvQyxNQUFNRyxNQUFNLENBQUMsVUFBVUosT0FBT25DLE1BQU07UUFFeEQsT0FBTyxJQUFJLENBQUNWLE9BQU8sQ0FBQyxjQUErQixPQUFqQjhDLE1BQU1JLFFBQVE7SUFDbEQ7SUFFQSxNQUFNNEIsWUFBWXZCLEVBQVUsRUFBRTtRQUM1QixPQUFPLElBQUksQ0FBQ3ZELE9BQU8sQ0FBQyxjQUFpQixPQUFIdUQ7SUFDcEM7SUFFQSxNQUFNd0IsZUFBZUMsWUFBaUIsRUFBRTtRQUN0QyxPQUFPLElBQUksQ0FBQ2hGLE9BQU8sQ0FBQyxjQUFjO1lBQ2hDNkIsUUFBUTtZQUNSQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNnRDtRQUN2QjtJQUNGO0lBRUEsTUFBTUMsZUFBZUMsSUFBVSxFQUFFQyxRQU1oQyxFQUFFO1FBQ0QsTUFBTUMsV0FBVyxJQUFJQztRQUNyQkQsU0FBU25DLE1BQU0sQ0FBQyxRQUFRaUM7UUFDeEJFLFNBQVNuQyxNQUFNLENBQUMsU0FBU2tDLFNBQVNHLEtBQUs7UUFDdkMsSUFBSUgsU0FBU1AsYUFBYSxFQUFFUSxTQUFTbkMsTUFBTSxDQUFDLGlCQUFpQmtDLFNBQVNQLGFBQWE7UUFDbkYsSUFBSU8sU0FBU04sT0FBTyxFQUFFTyxTQUFTbkMsTUFBTSxDQUFDLFdBQVdrQyxTQUFTTixPQUFPO1FBQ2pFLElBQUlNLFNBQVNJLFNBQVMsRUFBRUgsU0FBU25DLE1BQU0sQ0FBQyxhQUFha0MsU0FBU0ksU0FBUztRQUN2RSxJQUFJSixTQUFTSyxlQUFlLEtBQUtDLFdBQVdMLFNBQVNuQyxNQUFNLENBQUMsbUJBQW1Ca0MsU0FBU0ssZUFBZSxDQUFDdEMsUUFBUTtRQUVoSCxNQUFNN0MsVUFBa0MsQ0FBQztRQUN6QyxJQUFJLElBQUksQ0FBQ1YsS0FBSyxFQUFFO1lBQ2RVLFFBQVFDLGFBQWEsR0FBRyxVQUFxQixPQUFYLElBQUksQ0FBQ1gsS0FBSztRQUM5QztRQUVBLElBQUk7WUFDRixNQUFNWSxXQUFXLE1BQU1DLE1BQU0sR0FBZ0IsT0FBYixJQUFJLENBQUNKLE9BQU8sRUFBQyxzQkFBb0I7Z0JBQy9EeUIsUUFBUTtnQkFDUnhCO2dCQUNBeUIsTUFBTXNEO1lBQ1I7WUFFQSxJQUFJLENBQUM3RSxTQUFTRSxFQUFFLEVBQUU7Z0JBQ2hCLE1BQU1XLFlBQVksTUFBTWIsU0FBU1EsSUFBSSxHQUFHTSxLQUFLLENBQUMsSUFBTyxFQUFDO2dCQUN0RCxNQUFNLElBQUlGLE1BQU1DLFVBQVVFLE1BQU0sSUFBSUYsVUFBVUcsT0FBTyxJQUFJLFFBQXdCLE9BQWhCaEIsU0FBU0csTUFBTTtZQUNsRjtZQUVBLE1BQU1JLE9BQU8sTUFBTVAsU0FBU1EsSUFBSTtZQUNoQyxPQUFPO2dCQUFFRDtZQUFLO1FBQ2hCLEVBQUUsT0FBT1UsT0FBTztZQUNkQyxRQUFRRCxLQUFLLENBQUMsMkJBQTJCQTtZQUN6QyxPQUFPO2dCQUFFQSxPQUFPQSxpQkFBaUJMLFFBQVFLLE1BQU1ELE9BQU8sR0FBRztZQUFnQjtRQUMzRTtJQUNGO0lBRUEsTUFBTW1FLGVBQWVuQyxFQUFVLEVBQUV5QixZQUFpQixFQUFFO1FBQ2xELE9BQU8sSUFBSSxDQUFDaEYsT0FBTyxDQUFDLGNBQWlCLE9BQUh1RCxLQUFNO1lBQ3RDMUIsUUFBUTtZQUNSQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNnRDtRQUN2QjtJQUNGO0lBRUEsTUFBTVcsZUFBZXBDLEVBQVUsRUFBRTtRQUMvQixPQUFPLElBQUksQ0FBQ3ZELE9BQU8sQ0FBQyxjQUFpQixPQUFIdUQsS0FBTTtZQUFFMUIsUUFBUTtRQUFTO0lBQzdEO0lBRUEsOEJBQThCO0lBQzlCLE1BQU0rRCxnQkFBZ0JyQyxFQUFVLEVBQUVzQyxZQUFxQixFQUFFQyxVQUFxQixFQUFFO1FBQzlFLE9BQU8sSUFBSSxDQUFDOUYsT0FBTyxDQUFDLGNBQWlCLE9BQUh1RCxJQUFHLGFBQVc7WUFDOUMxQixRQUFRO1lBQ1JDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQztnQkFDbkIrRCxlQUFlRixnQkFBZ0I7Z0JBQy9CRyxhQUFhRixjQUFjO29CQUFDO29CQUFhO29CQUFTO2lCQUFPO1lBQzNEO1FBQ0Y7SUFDRjtJQUVBLE1BQU1HLG9CQUFvQjFDLEVBQVUsRUFBRTtRQUNwQyxPQUFPLElBQUksQ0FBQ3ZELE9BQU8sQ0FBQyxjQUFpQixPQUFIdUQsSUFBRztJQUN2QztJQUVBLE1BQU0yQyxtQkFBbUJDLFdBQXFCLEVBQUVDLGNBQXVCLEVBQUU7UUFDdkUsT0FBTyxJQUFJLENBQUNwRyxPQUFPLENBQUMsOEJBQThCO1lBQ2hENkIsUUFBUTtZQUNSQyxNQUFNQyxLQUFLQyxTQUFTLENBQUM7Z0JBQ25CcUUsY0FBY0Y7Z0JBQ2RHLGlCQUFpQkYsa0JBQWtCO1lBQ3JDO1FBQ0Y7SUFDRjtJQUVBLE1BQU1HLG1CQUFtQjFELE1BQTBDLEVBQUU7UUFDbkUsTUFBTUMsUUFBUSxJQUFJQztRQUNsQixJQUFJRixtQkFBQUEsNkJBQUFBLE9BQVFHLElBQUksRUFBRUYsTUFBTUcsTUFBTSxDQUFDLFFBQVFKLE9BQU9HLElBQUksQ0FBQ0UsUUFBUTtRQUMzRCxJQUFJTCxtQkFBQUEsNkJBQUFBLE9BQVFNLEtBQUssRUFBRUwsTUFBTUcsTUFBTSxDQUFDLFNBQVNKLE9BQU9NLEtBQUssQ0FBQ0QsUUFBUTtRQUM5RCxPQUFPLElBQUksQ0FBQ2xELE9BQU8sQ0FBQywrQkFBcUMsT0FBTjhDO0lBQ3JEO0lBRUEsTUFBTTBELG1CQUFtQmpELEVBQVUsRUFBRTtRQUNuQyxPQUFPLElBQUksQ0FBQ3ZELE9BQU8sQ0FBQyxjQUFpQixPQUFIdUQsSUFBRztJQUN2QztJQUVBLE1BQU1rRCx1QkFBdUI7UUFDM0IsT0FBTyxJQUFJLENBQUN6RyxPQUFPLENBQUM7SUFDdEI7SUFFQSxNQUFNMEcscUJBQXFCN0QsTUFBbUUsRUFBRTtRQUM5RixNQUFNQyxRQUFRLElBQUlDO1FBQ2xCLElBQUlGLG1CQUFBQSw2QkFBQUEsT0FBUUcsSUFBSSxFQUFFRixNQUFNRyxNQUFNLENBQUMsUUFBUUosT0FBT0csSUFBSSxDQUFDRSxRQUFRO1FBQzNELElBQUlMLG1CQUFBQSw2QkFBQUEsT0FBUU0sS0FBSyxFQUFFTCxNQUFNRyxNQUFNLENBQUMsU0FBU0osT0FBT00sS0FBSyxDQUFDRCxRQUFRO1FBQzlELElBQUlMLG1CQUFBQSw2QkFBQUEsT0FBUThELGNBQWMsRUFBRTdELE1BQU1HLE1BQU0sQ0FBQyxrQkFBa0JKLE9BQU84RCxjQUFjLENBQUN6RCxRQUFRO1FBQ3pGLE9BQU8sSUFBSSxDQUFDbEQsT0FBTyxDQUFDLHdCQUE4QixPQUFOOEM7SUFDOUM7SUFFQSw2QkFBNkI7SUFDN0IsTUFBTThELG1CQUFtQi9ELE1BTXhCLEVBQUU7UUFDRCxNQUFNQyxRQUFRLElBQUlDO1FBQ2xCLElBQUlGLG1CQUFBQSw2QkFBQUEsT0FBUUcsSUFBSSxFQUFFRixNQUFNRyxNQUFNLENBQUMsUUFBUUosT0FBT0csSUFBSSxDQUFDRSxRQUFRO1FBQzNELElBQUlMLG1CQUFBQSw2QkFBQUEsT0FBUU0sS0FBSyxFQUFFTCxNQUFNRyxNQUFNLENBQUMsU0FBU0osT0FBT00sS0FBSyxDQUFDRCxRQUFRO1FBQzlELElBQUlMLG1CQUFBQSw2QkFBQUEsT0FBUWdFLFVBQVUsRUFBRS9ELE1BQU1HLE1BQU0sQ0FBQyxjQUFjSixPQUFPZ0UsVUFBVTtRQUNwRSxJQUFJaEUsbUJBQUFBLDZCQUFBQSxPQUFRZ0MsT0FBTyxFQUFFL0IsTUFBTUcsTUFBTSxDQUFDLFdBQVdKLE9BQU9nQyxPQUFPO1FBQzNELElBQUloQyxtQkFBQUEsNkJBQUFBLE9BQVFuQyxNQUFNLEVBQUVvQyxNQUFNRyxNQUFNLENBQUMsVUFBVUosT0FBT25DLE1BQU07UUFFeEQsT0FBTyxJQUFJLENBQUNWLE9BQU8sQ0FBQyxxQkFBc0MsT0FBakI4QyxNQUFNSSxRQUFRO0lBQ3pEO0lBRUEsTUFBTTRELGtCQUFrQnZELEVBQVUsRUFBRTtRQUNsQyxPQUFPLElBQUksQ0FBQ3ZELE9BQU8sQ0FBQyxxQkFBd0IsT0FBSHVEO0lBQzNDO0lBRUEsTUFBTXdELHFCQUFxQkMsU0FBYyxFQUFFO1FBQ3pDLE9BQU8sSUFBSSxDQUFDaEgsT0FBTyxDQUFDLHFCQUFxQjtZQUN2QzZCLFFBQVE7WUFDUkMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDZ0Y7UUFDdkI7SUFDRjtJQUVBLE1BQU1DLHFCQUFxQjFELEVBQVUsRUFBRXlELFNBQWMsRUFBRTtRQUNyRCxPQUFPLElBQUksQ0FBQ2hILE9BQU8sQ0FBQyxxQkFBd0IsT0FBSHVELEtBQU07WUFDN0MxQixRQUFRO1lBQ1JDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQ2dGO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNRSxxQkFBcUIzRCxFQUFVLEVBQUU7UUFDckMsT0FBTyxJQUFJLENBQUN2RCxPQUFPLENBQUMscUJBQXdCLE9BQUh1RCxLQUFNO1lBQUUxQixRQUFRO1FBQVM7SUFDcEU7SUFFQSxNQUFNc0YsbUJBQW1CNUQsRUFBVSxFQUFFNkQsV0FBZ0IsRUFBRTtRQUNyRCxPQUFPLElBQUksQ0FBQ3BILE9BQU8sQ0FBQyxxQkFBd0IsT0FBSHVELElBQUcsY0FBWTtZQUN0RDFCLFFBQVE7WUFDUkMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDb0Y7UUFDdkI7SUFDRjtJQUVBLE1BQU1DLGlCQUFpQjlELEVBQVUsRUFBRTtRQUNqQyxPQUFPLElBQUksQ0FBQ3ZELE9BQU8sQ0FBQyxxQkFBd0IsT0FBSHVELElBQUc7SUFDOUM7SUFFQSxNQUFNK0QscUJBQXFCO1FBQ3pCLE9BQU8sSUFBSSxDQUFDdEgsT0FBTyxDQUFDO0lBQ3RCO0lBRUEseUJBQXlCO0lBQ3pCLE1BQU11SCx5QkFBeUI7UUFDN0IsT0FBTyxJQUFJLENBQUN2SCxPQUFPLENBQUM7SUFDdEI7SUFFQSxNQUFNd0gsMEJBQTBCQyxPQUFZLEVBQUU7UUFDNUMsT0FBTyxJQUFJLENBQUN6SCxPQUFPLENBQUMscUJBQXFCO1lBQ3ZDNkIsUUFBUTtZQUNSQyxNQUFNQyxLQUFLQyxTQUFTLENBQUN5RjtRQUN2QjtJQUNGO0lBRUEsTUFBTUMsdUJBQXVCO1FBQzNCLE9BQU8sSUFBSSxDQUFDMUgsT0FBTyxDQUFDO0lBQ3RCO0lBRUEseUJBQXlCO0lBQ3pCLE1BQU0ySCxpQkFBaUI5RSxNQU90QixFQUFFO1FBQ0QsTUFBTUMsUUFBUSxJQUFJQztRQUNsQixJQUFJRixtQkFBQUEsNkJBQUFBLE9BQVFHLElBQUksRUFBRUYsTUFBTUcsTUFBTSxDQUFDLFFBQVFKLE9BQU9HLElBQUksQ0FBQ0UsUUFBUTtRQUMzRCxJQUFJTCxtQkFBQUEsNkJBQUFBLE9BQVFNLEtBQUssRUFBRUwsTUFBTUcsTUFBTSxDQUFDLFNBQVNKLE9BQU9NLEtBQUssQ0FBQ0QsUUFBUTtRQUM5RCxJQUFJTCxtQkFBQUEsNkJBQUFBLE9BQVFuQyxNQUFNLEVBQUVvQyxNQUFNRyxNQUFNLENBQUMsVUFBVUosT0FBT25DLE1BQU07UUFDeEQsSUFBSW1DLG1CQUFBQSw2QkFBQUEsT0FBUStFLGlCQUFpQixFQUFFOUUsTUFBTUcsTUFBTSxDQUFDLHFCQUFxQkosT0FBTytFLGlCQUFpQjtRQUN6RixJQUFJL0UsbUJBQUFBLDZCQUFBQSxPQUFRZ0MsT0FBTyxFQUFFL0IsTUFBTUcsTUFBTSxDQUFDLFdBQVdKLE9BQU9nQyxPQUFPO1FBQzNELElBQUloQyxtQkFBQUEsNkJBQUFBLE9BQVFnRixhQUFhLEVBQUUvRSxNQUFNRyxNQUFNLENBQUMsaUJBQWlCSixPQUFPZ0YsYUFBYSxDQUFDM0UsUUFBUTtRQUV0RixPQUFPLElBQUksQ0FBQ2xELE9BQU8sQ0FBQyxtQkFBb0MsT0FBakI4QyxNQUFNSSxRQUFRO0lBQ3ZEO0lBRUEsTUFBTTRFLGdCQUFnQnZFLEVBQVUsRUFBRTtRQUNoQyxPQUFPLElBQUksQ0FBQ3ZELE9BQU8sQ0FBQyxrQkFBcUIsT0FBSHVEO0lBQ3hDO0lBRUEsTUFBTXdFLG1CQUFtQkMsZ0JBQXFCLEVBQUU7UUFDOUMsT0FBTyxJQUFJLENBQUNoSSxPQUFPLENBQUMsbUJBQW1CO1lBQ3JDNkIsUUFBUTtZQUNSQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNnRztRQUN2QjtJQUNGO0lBRUEsTUFBTUMsbUJBQW1CMUUsRUFBVSxFQUFFeUUsZ0JBQXFCLEVBQUU7UUFDMUQsT0FBTyxJQUFJLENBQUNoSSxPQUFPLENBQUMsa0JBQXFCLE9BQUh1RCxLQUFNO1lBQzFDMUIsUUFBUTtZQUNSQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNnRztRQUN2QjtJQUNGO0lBRUEsTUFBTUUsbUJBQW1CM0UsRUFBVSxFQUFFO1FBQ25DLE9BQU8sSUFBSSxDQUFDdkQsT0FBTyxDQUFDLGtCQUFxQixPQUFIdUQsS0FBTTtZQUFFMUIsUUFBUTtRQUFTO0lBQ2pFO0lBRUEsTUFBTXNHLDBCQUEwQjVFLEVBQVUsRUFBRTtRQUMxQyxPQUFPLElBQUksQ0FBQ3ZELE9BQU8sQ0FBQyxrQkFBcUIsT0FBSHVELElBQUcsYUFBVztZQUFFMUIsUUFBUTtRQUFPO0lBQ3ZFO0lBRUEsTUFBTXVHLHVCQUF1QkMsTUFBYyxFQUFFeEYsTUFBMEMsRUFBRTtRQUN2RixNQUFNQyxRQUFRLElBQUlDO1FBQ2xCLElBQUlGLG1CQUFBQSw2QkFBQUEsT0FBUUcsSUFBSSxFQUFFRixNQUFNRyxNQUFNLENBQUMsUUFBUUosT0FBT0csSUFBSSxDQUFDRSxRQUFRO1FBQzNELElBQUlMLG1CQUFBQSw2QkFBQUEsT0FBUU0sS0FBSyxFQUFFTCxNQUFNRyxNQUFNLENBQUMsU0FBU0osT0FBT00sS0FBSyxDQUFDRCxRQUFRO1FBRTlELE9BQU8sSUFBSSxDQUFDbEQsT0FBTyxDQUFDLHdCQUFrQzhDLE9BQVZ1RixRQUFPLEtBQW9CLE9BQWpCdkYsTUFBTUksUUFBUTtJQUN0RTtJQUVBLE1BQU1vRix1QkFBdUJ6RixNQUEwQyxFQUFFO1FBQ3ZFLE1BQU1DLFFBQVEsSUFBSUM7UUFDbEIsSUFBSUYsbUJBQUFBLDZCQUFBQSxPQUFRRyxJQUFJLEVBQUVGLE1BQU1HLE1BQU0sQ0FBQyxRQUFRSixPQUFPRyxJQUFJLENBQUNFLFFBQVE7UUFDM0QsSUFBSUwsbUJBQUFBLDZCQUFBQSxPQUFRTSxLQUFLLEVBQUVMLE1BQU1HLE1BQU0sQ0FBQyxTQUFTSixPQUFPTSxLQUFLLENBQUNELFFBQVE7UUFFOUQsT0FBTyxJQUFJLENBQUNsRCxPQUFPLENBQUMseUJBQTBDLE9BQWpCOEMsTUFBTUksUUFBUTtJQUM3RDtJQUVBLGtCQUFrQjtJQUNsQixNQUFNcUYsc0JBQXNCQyxjQUFzQixFQUFFM0YsTUFBMEMsRUFBRTtRQUM5RixNQUFNQyxRQUFRLElBQUlDO1FBQ2xCLElBQUlGLG1CQUFBQSw2QkFBQUEsT0FBUUcsSUFBSSxFQUFFRixNQUFNRyxNQUFNLENBQUMsUUFBUUosT0FBT0csSUFBSSxDQUFDRSxRQUFRO1FBQzNELElBQUlMLG1CQUFBQSw2QkFBQUEsT0FBUU0sS0FBSyxFQUFFTCxNQUFNRyxNQUFNLENBQUMsU0FBU0osT0FBT00sS0FBSyxDQUFDRCxRQUFRO1FBRTlELE9BQU8sSUFBSSxDQUFDbEQsT0FBTyxDQUFDLGtCQUEyQzhDLE9BQXpCMEYsZ0JBQWUsWUFBMkIsT0FBakIxRixNQUFNSSxRQUFRO0lBQy9FO0lBRUEsTUFBTXVGLFlBQVlELGNBQXNCLEVBQUVFLFNBQWMsRUFBRTtRQUN4RCxPQUFPLElBQUksQ0FBQzFJLE9BQU8sQ0FBQyxrQkFBaUMsT0FBZndJLGdCQUFlLGFBQVc7WUFDOUQzRyxRQUFRO1lBQ1JDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQzBHO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNQyxTQUFTSCxjQUFzQixFQUFFSSxPQUFlLEVBQUU7UUFDdEQsT0FBTyxJQUFJLENBQUM1SSxPQUFPLENBQUMsa0JBQTJDNEksT0FBekJKLGdCQUFlLFlBQWtCLE9BQVJJO0lBQ2pFO0lBRUEsTUFBTUMsWUFBWUwsY0FBc0IsRUFBRUksT0FBZSxFQUFFRixTQUFjLEVBQUU7UUFDekUsT0FBTyxJQUFJLENBQUMxSSxPQUFPLENBQUMsa0JBQTJDNEksT0FBekJKLGdCQUFlLFlBQWtCLE9BQVJJLFVBQVc7WUFDeEUvRyxRQUFRO1lBQ1JDLE1BQU1DLEtBQUtDLFNBQVMsQ0FBQzBHO1FBQ3ZCO0lBQ0Y7SUFFQSxNQUFNSSxZQUFZTixjQUFzQixFQUFFSSxPQUFlLEVBQUU7UUFDekQsT0FBTyxJQUFJLENBQUM1SSxPQUFPLENBQUMsa0JBQTJDNEksT0FBekJKLGdCQUFlLFlBQWtCLE9BQVJJLFVBQVc7WUFBRS9HLFFBQVE7UUFBUztJQUMvRjtJQUVBLE1BQU1rSCxlQUFlUCxjQUFzQixFQUFFSSxPQUFlLEVBQUVJLGNBQXNCLEVBQUU7UUFDcEYsT0FBTyxJQUFJLENBQUNoSixPQUFPLENBQUMsa0JBQTJDNEksT0FBekJKLGdCQUFlLFlBQWdEUSxPQUF0Q0osU0FBUSxnQ0FBNkMsT0FBZkksaUJBQWtCO1lBQ3JIbkgsUUFBUTtRQUNWO0lBQ0Y7SUFFQSxxQkFBcUI7SUFDckIsTUFBTW9ILHlCQUF5QnBHLE1BTTlCLEVBQUU7UUFDRCxNQUFNQyxRQUFRLElBQUlDO1FBQ2xCLElBQUlGLG1CQUFBQSw2QkFBQUEsT0FBUUcsSUFBSSxFQUFFRixNQUFNRyxNQUFNLENBQUMsUUFBUUosT0FBT0csSUFBSSxDQUFDRSxRQUFRO1FBQzNELElBQUlMLG1CQUFBQSw2QkFBQUEsT0FBUU0sS0FBSyxFQUFFTCxNQUFNRyxNQUFNLENBQUMsU0FBU0osT0FBT00sS0FBSyxDQUFDRCxRQUFRO1FBQzlELElBQUlMLG1CQUFBQSw2QkFBQUEsT0FBUXFHLFFBQVEsRUFBRXBHLE1BQU1HLE1BQU0sQ0FBQyxZQUFZSixPQUFPcUcsUUFBUTtRQUM5RCxJQUFJckcsbUJBQUFBLDZCQUFBQSxPQUFRc0csYUFBYSxFQUFFckcsTUFBTUcsTUFBTSxDQUFDLGlCQUFpQkosT0FBT3NHLGFBQWEsQ0FBQ2pHLFFBQVE7UUFDdEYsSUFBSUwsbUJBQUFBLDZCQUFBQSxPQUFRdUcsV0FBVyxFQUFFdEcsTUFBTUcsTUFBTSxDQUFDLGVBQWVKLE9BQU91RyxXQUFXLENBQUNsRyxRQUFRO1FBRWhGLE9BQU8sSUFBSSxDQUFDbEQsT0FBTyxDQUFDLDZCQUE4QyxPQUFqQjhDLE1BQU1JLFFBQVE7SUFDakU7SUFFQSxNQUFNbUcsMkJBQTJCQyxZQUFpQixFQUFFO1FBQ2xELE9BQU8sSUFBSSxDQUFDdEosT0FBTyxDQUFDLDZCQUE2QjtZQUMvQzZCLFFBQVE7WUFDUkMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDc0g7UUFDdkI7SUFDRjtJQUVBLE1BQU1DLHdCQUF3QmhHLEVBQVUsRUFBRTtRQUN4QyxPQUFPLElBQUksQ0FBQ3ZELE9BQU8sQ0FBQyw0QkFBK0IsT0FBSHVEO0lBQ2xEO0lBRUEsTUFBTWlHLCtCQUErQkMsVUFBa0IsRUFBRXpCLGdCQUFxQixFQUFFO1FBQzlFLE9BQU8sSUFBSSxDQUFDaEksT0FBTyxDQUFDLDRCQUF1QyxPQUFYeUosWUFBVyxTQUFPO1lBQ2hFNUgsUUFBUTtZQUNSQyxNQUFNQyxLQUFLQyxTQUFTLENBQUNnRztRQUN2QjtJQUNGO0lBRUEsb0JBQW9CO0lBQ3BCLE1BQU0wQix3QkFBd0JsQixjQUFzQixFQUFFM0YsTUFJckQsRUFBRTtRQUNELE1BQU1DLFFBQVEsSUFBSUM7UUFDbEIsSUFBSUYsbUJBQUFBLDZCQUFBQSxPQUFRRyxJQUFJLEVBQUVGLE1BQU1HLE1BQU0sQ0FBQyxRQUFRSixPQUFPRyxJQUFJLENBQUNFLFFBQVE7UUFDM0QsSUFBSUwsbUJBQUFBLDZCQUFBQSxPQUFRTSxLQUFLLEVBQUVMLE1BQU1HLE1BQU0sQ0FBQyxTQUFTSixPQUFPTSxLQUFLLENBQUNELFFBQVE7UUFDOUQsSUFBSUwsQ0FBQUEsbUJBQUFBLDZCQUFBQSxPQUFROEcsYUFBYSxNQUFLbEUsV0FBVzNDLE1BQU1HLE1BQU0sQ0FBQyxpQkFBaUJKLE9BQU84RyxhQUFhLENBQUN6RyxRQUFRO1FBRXBHLE9BQU8sSUFBSSxDQUFDbEQsT0FBTyxDQUFDLGtCQUE2QzhDLE9BQTNCMEYsZ0JBQWUsY0FBNkIsT0FBakIxRixNQUFNSSxRQUFRO0lBQ2pGO0lBRUEsTUFBTTBHLDBCQUEwQnBCLGNBQXNCLEVBQUVxQixXQUFnQixFQUFFO1FBQ3hFLE9BQU8sSUFBSSxDQUFDN0osT0FBTyxDQUFDLGtCQUFpQyxPQUFmd0ksZ0JBQWUsZUFBYTtZQUNoRTNHLFFBQVE7WUFDUkMsTUFBTUMsS0FBS0MsU0FBUyxDQUFDNkg7UUFDdkI7SUFDRjtJQUVBLE1BQU1DLDJCQUEyQnRCLGNBQXNCLEVBQUV1QixTQUFpQixFQUFFO1FBQzFFLE9BQU8sSUFBSSxDQUFDL0osT0FBTyxDQUFDLGtCQUE2QytKLE9BQTNCdkIsZ0JBQWUsY0FBc0IsT0FBVnVCLFdBQVUsYUFBVztZQUNwRmxJLFFBQVE7UUFDVjtJQUNGO0lBRUEsMEJBQTBCO0lBQzFCLE1BQU1tSSwwQkFBMEJ4QixjQUFzQixFQUFFM0YsTUFBMEMsRUFBRTtRQUNsRyxNQUFNQyxRQUFRLElBQUlDO1FBQ2xCLElBQUlGLG1CQUFBQSw2QkFBQUEsT0FBUUcsSUFBSSxFQUFFRixNQUFNRyxNQUFNLENBQUMsUUFBUUosT0FBT0csSUFBSSxDQUFDRSxRQUFRO1FBQzNELElBQUlMLG1CQUFBQSw2QkFBQUEsT0FBUU0sS0FBSyxFQUFFTCxNQUFNRyxNQUFNLENBQUMsU0FBU0osT0FBT00sS0FBSyxDQUFDRCxRQUFRO1FBRTlELE9BQU8sSUFBSSxDQUFDbEQsT0FBTyxDQUFDLGtCQUErQzhDLE9BQTdCMEYsZ0JBQWUsZ0JBQStCLE9BQWpCMUYsTUFBTUksUUFBUTtJQUNuRjtJQUVBLE1BQU0rRywyQkFBMkJ6QixjQUFzQixFQUFFO1FBQ3ZELE9BQU8sSUFBSSxDQUFDeEksT0FBTyxDQUFDLGtCQUFpQyxPQUFmd0ksZ0JBQWU7SUFDdkQ7SUFob0JBMEIsWUFBWTlKLFVBQWtCZixZQUFZLENBQUU7YUFGcENNLFFBQXVCO1FBRzdCLElBQUksQ0FBQ1MsT0FBTyxHQUFHQTtRQUVmLDJDQUEyQztRQUMzQyxJQUFJLElBQWtCLEVBQWE7WUFDakMsSUFBSSxDQUFDVCxLQUFLLEdBQUdDLGFBQWFnQyxPQUFPLENBQUM7UUFDcEM7SUFDRjtBQTBuQkY7QUFFQSw0QkFBNEI7QUFDckIsTUFBTXVJLFlBQVksSUFBSTFLLFlBQVkiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbGliL2FwaS1jbGllbnQudHM/YjA5OSJdLCJzb3VyY2VzQ29udGVudCI6WyIvKipcbiAqIEFQSSBDbGllbnQgZm9yIERlYWxWZXJzZSBPUyBCYWNrZW5kXG4gKi9cblxuY29uc3QgQVBJX0JBU0VfVVJMID0gcHJvY2Vzcy5lbnYuTkVYVF9QVUJMSUNfQVBJX1VSTCB8fCAnaHR0cDovL2xvY2FsaG9zdDo4MDAwL2FwaS92MSc7XG5cbmludGVyZmFjZSBBcGlSZXNwb25zZTxUPiB7XG4gIGRhdGE/OiBUO1xuICBlcnJvcj86IHN0cmluZztcbiAgbWVzc2FnZT86IHN0cmluZztcbn1cblxuY2xhc3MgQXBpQ2xpZW50IHtcbiAgcHJpdmF0ZSBiYXNlVXJsOiBzdHJpbmc7XG4gIHByaXZhdGUgdG9rZW46IHN0cmluZyB8IG51bGwgPSBudWxsO1xuXG4gIGNvbnN0cnVjdG9yKGJhc2VVcmw6IHN0cmluZyA9IEFQSV9CQVNFX1VSTCkge1xuICAgIHRoaXMuYmFzZVVybCA9IGJhc2VVcmw7XG5cbiAgICAvLyBHZXQgdG9rZW4gZnJvbSBsb2NhbFN0b3JhZ2UgaWYgYXZhaWxhYmxlXG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICB0aGlzLnRva2VuID0gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ2FjY2Vzc190b2tlbicpO1xuICAgIH1cbiAgfVxuXG4gIHNldFRva2VuKHRva2VuOiBzdHJpbmcpIHtcbiAgICB0aGlzLnRva2VuID0gdG9rZW47XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgnYWNjZXNzX3Rva2VuJywgdG9rZW4pO1xuICAgIH1cbiAgfVxuXG4gIGNsZWFyVG9rZW4oKSB7XG4gICAgdGhpcy50b2tlbiA9IG51bGw7XG4gICAgaWYgKHR5cGVvZiB3aW5kb3cgIT09ICd1bmRlZmluZWQnKSB7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgnYWNjZXNzX3Rva2VuJyk7XG4gICAgICBsb2NhbFN0b3JhZ2UucmVtb3ZlSXRlbSgncmVmcmVzaF90b2tlbicpO1xuICAgIH1cbiAgfVxuXG4gIHByaXZhdGUgYXN5bmMgcmVxdWVzdDxUPihcbiAgICBlbmRwb2ludDogc3RyaW5nLFxuICAgIG9wdGlvbnM6IFJlcXVlc3RJbml0ID0ge31cbiAgKTogUHJvbWlzZTxBcGlSZXNwb25zZTxUPj4ge1xuICAgIGNvbnN0IHVybCA9IGAke3RoaXMuYmFzZVVybH0ke2VuZHBvaW50fWA7XG5cbiAgICBjb25zdCBoZWFkZXJzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge1xuICAgICAgJ0NvbnRlbnQtVHlwZSc6ICdhcHBsaWNhdGlvbi9qc29uJyxcbiAgICAgIC4uLihvcHRpb25zLmhlYWRlcnMgYXMgUmVjb3JkPHN0cmluZywgc3RyaW5nPiB8fCB7fSksXG4gICAgfTtcblxuICAgIGlmICh0aGlzLnRva2VuKSB7XG4gICAgICBoZWFkZXJzLkF1dGhvcml6YXRpb24gPSBgQmVhcmVyICR7dGhpcy50b2tlbn1gO1xuICAgIH1cblxuXG5cbiAgICB0cnkge1xuICAgICAgY29uc3QgcmVzcG9uc2UgPSBhd2FpdCBmZXRjaCh1cmwsIHtcbiAgICAgICAgLi4ub3B0aW9ucyxcbiAgICAgICAgaGVhZGVycyxcbiAgICAgIH0pO1xuXG4gICAgICBpZiAoIXJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGlmIChyZXNwb25zZS5zdGF0dXMgPT09IDQwMSkge1xuICAgICAgICAgIC8vIFRva2VuIGV4cGlyZWQsIHRyeSB0byByZWZyZXNoXG4gICAgICAgICAgY29uc3QgcmVmcmVzaGVkID0gYXdhaXQgdGhpcy5yZWZyZXNoVG9rZW4oKTtcbiAgICAgICAgICBpZiAocmVmcmVzaGVkKSB7XG4gICAgICAgICAgICAvLyBSZXRyeSB0aGUgcmVxdWVzdCB3aXRoIG5ldyB0b2tlblxuICAgICAgICAgICAgaGVhZGVycy5BdXRob3JpemF0aW9uID0gYEJlYXJlciAke3RoaXMudG9rZW59YDtcbiAgICAgICAgICAgIGNvbnN0IHJldHJ5UmVzcG9uc2UgPSBhd2FpdCBmZXRjaCh1cmwsIHtcbiAgICAgICAgICAgICAgLi4ub3B0aW9ucyxcbiAgICAgICAgICAgICAgaGVhZGVycyxcbiAgICAgICAgICAgIH0pO1xuICAgICAgICAgICAgXG4gICAgICAgICAgICBpZiAocmV0cnlSZXNwb25zZS5vaykge1xuICAgICAgICAgICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmV0cnlSZXNwb25zZS5qc29uKCk7XG4gICAgICAgICAgICAgIHJldHVybiB7IGRhdGEgfTtcbiAgICAgICAgICAgIH1cbiAgICAgICAgICB9XG4gICAgICAgICAgXG4gICAgICAgICAgLy8gUmVmcmVzaCBmYWlsZWQsIGNsZWFyIHRva2VucyBhbmQgcmVkaXJlY3QgdG8gbG9naW5cbiAgICAgICAgICB0aGlzLmNsZWFyVG9rZW4oKTtcbiAgICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICAgIHdpbmRvdy5sb2NhdGlvbi5ocmVmID0gJy9hdXRoL2xvZ2luJztcbiAgICAgICAgICB9XG4gICAgICAgICAgdGhyb3cgbmV3IEVycm9yKCdBdXRoZW50aWNhdGlvbiBmYWlsZWQnKTtcbiAgICAgICAgfVxuXG4gICAgICAgIGNvbnN0IGVycm9yRGF0YSA9IGF3YWl0IHJlc3BvbnNlLmpzb24oKS5jYXRjaCgoKSA9PiAoe30pKTtcbiAgICAgICAgdGhyb3cgbmV3IEVycm9yKGVycm9yRGF0YS5kZXRhaWwgfHwgZXJyb3JEYXRhLm1lc3NhZ2UgfHwgYEhUVFAgJHtyZXNwb25zZS5zdGF0dXN9YCk7XG4gICAgICB9XG5cbiAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICByZXR1cm4geyBkYXRhIH07XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ0FQSSByZXF1ZXN0IGZhaWxlZDonLCBlcnJvcik7XG4gICAgICAvLyBDaGVjayBpZiBpdCdzIGEgbmV0d29yayBlcnJvclxuICAgICAgaWYgKGVycm9yIGluc3RhbmNlb2YgVHlwZUVycm9yICYmIGVycm9yLm1lc3NhZ2UuaW5jbHVkZXMoJ2ZldGNoJykpIHtcbiAgICAgICAgcmV0dXJuIHsgZXJyb3I6ICdVbmFibGUgdG8gY29ubmVjdCB0byBzZXJ2ZXIuIFBsZWFzZSBjaGVjayB5b3VyIGNvbm5lY3Rpb24gYW5kIHRyeSBhZ2Fpbi4nIH07XG4gICAgICB9XG4gICAgICByZXR1cm4geyBlcnJvcjogZXJyb3IgaW5zdGFuY2VvZiBFcnJvciA/IGVycm9yLm1lc3NhZ2UgOiAnVW5rbm93biBlcnJvcicgfTtcbiAgICB9XG4gIH1cblxuICBwcml2YXRlIGFzeW5jIHJlZnJlc2hUb2tlbigpOiBQcm9taXNlPGJvb2xlYW4+IHtcbiAgICBjb25zdCByZWZyZXNoVG9rZW4gPSB0eXBlb2Ygd2luZG93ICE9PSAndW5kZWZpbmVkJyBcbiAgICAgID8gbG9jYWxTdG9yYWdlLmdldEl0ZW0oJ3JlZnJlc2hfdG9rZW4nKSBcbiAgICAgIDogbnVsbDtcbiAgICBcbiAgICBpZiAoIXJlZnJlc2hUb2tlbikgcmV0dXJuIGZhbHNlO1xuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS9hdXRoL3JlZnJlc2hgLCB7XG4gICAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgICBoZWFkZXJzOiB7ICdDb250ZW50LVR5cGUnOiAnYXBwbGljYXRpb24vanNvbicgfSxcbiAgICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkoeyByZWZyZXNoX3Rva2VuOiByZWZyZXNoVG9rZW4gfSksXG4gICAgICB9KTtcblxuICAgICAgaWYgKHJlc3BvbnNlLm9rKSB7XG4gICAgICAgIGNvbnN0IGRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCk7XG4gICAgICAgIHRoaXMuc2V0VG9rZW4oZGF0YS5hY2Nlc3NfdG9rZW4pO1xuICAgICAgICBpZiAodHlwZW9mIHdpbmRvdyAhPT0gJ3VuZGVmaW5lZCcpIHtcbiAgICAgICAgICBsb2NhbFN0b3JhZ2Uuc2V0SXRlbSgncmVmcmVzaF90b2tlbicsIGRhdGEucmVmcmVzaF90b2tlbik7XG4gICAgICAgIH1cbiAgICAgICAgcmV0dXJuIHRydWU7XG4gICAgICB9XG4gICAgfSBjYXRjaCAoZXJyb3IpIHtcbiAgICAgIGNvbnNvbGUuZXJyb3IoJ1Rva2VuIHJlZnJlc2ggZmFpbGVkOicsIGVycm9yKTtcbiAgICB9XG5cbiAgICByZXR1cm4gZmFsc2U7XG4gIH1cblxuICAvLyBBdXRoZW50aWNhdGlvbiBlbmRwb2ludHNcbiAgYXN5bmMgbG9naW4oZW1haWw6IHN0cmluZywgcGFzc3dvcmQ6IHN0cmluZykge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3Q8e1xuICAgICAgYWNjZXNzX3Rva2VuOiBzdHJpbmc7XG4gICAgICByZWZyZXNoX3Rva2VuOiBzdHJpbmc7XG4gICAgICB0b2tlbl90eXBlOiBzdHJpbmc7XG4gICAgfT4oJy9hdXRoL2xvZ2luL2pzb24nLCB7XG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHsgZW1haWwsIHBhc3N3b3JkIH0pLFxuICAgIH0pO1xuICB9XG5cbiAgYXN5bmMgcmVnaXN0ZXIodXNlckRhdGE6IHtcbiAgICBlbWFpbDogc3RyaW5nO1xuICAgIHBhc3N3b3JkOiBzdHJpbmc7XG4gICAgZmlyc3RfbmFtZTogc3RyaW5nO1xuICAgIGxhc3RfbmFtZTogc3RyaW5nO1xuICAgIG9yZ2FuaXphdGlvbl9pZDogc3RyaW5nO1xuICB9KSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL2F1dGgvcmVnaXN0ZXInLCB7XG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHVzZXJEYXRhKSxcbiAgICB9KTtcbiAgfVxuXG4gIGFzeW5jIGxvZ291dCgpIHtcbiAgICBjb25zdCByZXN1bHQgPSBhd2FpdCB0aGlzLnJlcXVlc3QoJy9hdXRoL2xvZ291dCcsIHsgbWV0aG9kOiAnUE9TVCcgfSk7XG4gICAgdGhpcy5jbGVhclRva2VuKCk7XG4gICAgcmV0dXJuIHJlc3VsdDtcbiAgfVxuXG4gIC8vIFVzZXIgZW5kcG9pbnRzXG4gIGFzeW5jIGdldEN1cnJlbnRVc2VyKCkge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy91c2Vycy9tZScpO1xuICB9XG5cbiAgYXN5bmMgdXBkYXRlQ3VycmVudFVzZXIodXNlckRhdGE6IGFueSkge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy91c2Vycy9tZScsIHtcbiAgICAgIG1ldGhvZDogJ1BVVCcsXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh1c2VyRGF0YSksXG4gICAgfSk7XG4gIH1cblxuICBhc3luYyBnZXRVc2VycyhwYXJhbXM/OiB7IHNraXA/OiBudW1iZXI7IGxpbWl0PzogbnVtYmVyIH0pIHtcbiAgICBjb25zdCBxdWVyeSA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcbiAgICBpZiAocGFyYW1zPy5za2lwKSBxdWVyeS5hcHBlbmQoJ3NraXAnLCBwYXJhbXMuc2tpcC50b1N0cmluZygpKTtcbiAgICBpZiAocGFyYW1zPy5saW1pdCkgcXVlcnkuYXBwZW5kKCdsaW1pdCcsIHBhcmFtcy5saW1pdC50b1N0cmluZygpKTtcbiAgICBcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvdXNlcnM/JHtxdWVyeS50b1N0cmluZygpfWApO1xuICB9XG5cbiAgLy8gRGVhbCBlbmRwb2ludHNcbiAgYXN5bmMgZ2V0RGVhbHMocGFyYW1zPzoge1xuICAgIHNraXA/OiBudW1iZXI7XG4gICAgbGltaXQ/OiBudW1iZXI7XG4gICAgc3RhZ2U/OiBzdHJpbmc7XG4gICAgc3RhdHVzPzogc3RyaW5nO1xuICB9KSB7XG4gICAgY29uc3QgcXVlcnkgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XG4gICAgaWYgKHBhcmFtcz8uc2tpcCkgcXVlcnkuYXBwZW5kKCdza2lwJywgcGFyYW1zLnNraXAudG9TdHJpbmcoKSk7XG4gICAgaWYgKHBhcmFtcz8ubGltaXQpIHF1ZXJ5LmFwcGVuZCgnbGltaXQnLCBwYXJhbXMubGltaXQudG9TdHJpbmcoKSk7XG4gICAgaWYgKHBhcmFtcz8uc3RhZ2UpIHF1ZXJ5LmFwcGVuZCgnc3RhZ2UnLCBwYXJhbXMuc3RhZ2UpO1xuICAgIGlmIChwYXJhbXM/LnN0YXR1cykgcXVlcnkuYXBwZW5kKCdzdGF0dXMnLCBwYXJhbXMuc3RhdHVzKTtcbiAgICBcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvZGVhbHM/JHtxdWVyeS50b1N0cmluZygpfWApO1xuICB9XG5cbiAgYXN5bmMgZ2V0RGVhbChpZDogc3RyaW5nKSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL2RlYWxzLyR7aWR9YCk7XG4gIH1cblxuICBhc3luYyBjcmVhdGVEZWFsKGRlYWxEYXRhOiBhbnkpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KCcvZGVhbHMnLCB7XG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGRlYWxEYXRhKSxcbiAgICB9KTtcbiAgfVxuXG4gIGFzeW5jIHVwZGF0ZURlYWwoaWQ6IHN0cmluZywgZGVhbERhdGE6IGFueSkge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9kZWFscy8ke2lkfWAsIHtcbiAgICAgIG1ldGhvZDogJ1BVVCcsXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShkZWFsRGF0YSksXG4gICAgfSk7XG4gIH1cblxuICBhc3luYyBkZWxldGVEZWFsKGlkOiBzdHJpbmcpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvZGVhbHMvJHtpZH1gLCB7IG1ldGhvZDogJ0RFTEVURScgfSk7XG4gIH1cblxuICBhc3luYyBnZXREZWFsc1N0YXRzKCkge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy9kZWFscy9zdGF0cy9zdW1tYXJ5Jyk7XG4gIH1cblxuICAvLyBDbGllbnQgZW5kcG9pbnRzXG4gIGFzeW5jIGdldENsaWVudHMocGFyYW1zPzoge1xuICAgIHNraXA/OiBudW1iZXI7XG4gICAgbGltaXQ/OiBudW1iZXI7XG4gICAgY2xpZW50X3R5cGU/OiBzdHJpbmc7XG4gICAgaW5kdXN0cnk/OiBzdHJpbmc7XG4gICAgc2VhcmNoPzogc3RyaW5nO1xuICB9KSB7XG4gICAgY29uc3QgcXVlcnkgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XG4gICAgaWYgKHBhcmFtcz8uc2tpcCkgcXVlcnkuYXBwZW5kKCdza2lwJywgcGFyYW1zLnNraXAudG9TdHJpbmcoKSk7XG4gICAgaWYgKHBhcmFtcz8ubGltaXQpIHF1ZXJ5LmFwcGVuZCgnbGltaXQnLCBwYXJhbXMubGltaXQudG9TdHJpbmcoKSk7XG4gICAgaWYgKHBhcmFtcz8uY2xpZW50X3R5cGUpIHF1ZXJ5LmFwcGVuZCgnY2xpZW50X3R5cGUnLCBwYXJhbXMuY2xpZW50X3R5cGUpO1xuICAgIGlmIChwYXJhbXM/LmluZHVzdHJ5KSBxdWVyeS5hcHBlbmQoJ2luZHVzdHJ5JywgcGFyYW1zLmluZHVzdHJ5KTtcbiAgICBpZiAocGFyYW1zPy5zZWFyY2gpIHF1ZXJ5LmFwcGVuZCgnc2VhcmNoJywgcGFyYW1zLnNlYXJjaCk7XG4gICAgXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL2NsaWVudHM/JHtxdWVyeS50b1N0cmluZygpfWApO1xuICB9XG5cbiAgYXN5bmMgZ2V0Q2xpZW50KGlkOiBzdHJpbmcpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvY2xpZW50cy8ke2lkfWApO1xuICB9XG5cbiAgYXN5bmMgY3JlYXRlQ2xpZW50KGNsaWVudERhdGE6IGFueSkge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy9jbGllbnRzJywge1xuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShjbGllbnREYXRhKSxcbiAgICB9KTtcbiAgfVxuXG4gIGFzeW5jIHVwZGF0ZUNsaWVudChpZDogc3RyaW5nLCBjbGllbnREYXRhOiBhbnkpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvY2xpZW50cy8ke2lkfWAsIHtcbiAgICAgIG1ldGhvZDogJ1BVVCcsXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShjbGllbnREYXRhKSxcbiAgICB9KTtcbiAgfVxuXG4gIGFzeW5jIGRlbGV0ZUNsaWVudChpZDogc3RyaW5nKSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL2NsaWVudHMvJHtpZH1gLCB7IG1ldGhvZDogJ0RFTEVURScgfSk7XG4gIH1cblxuICAvLyBBbmFseXRpY3MgZW5kcG9pbnRzXG4gIGFzeW5jIGdldERhc2hib2FyZEFuYWx5dGljcygpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KCcvYW5hbHl0aWNzL2Rhc2hib2FyZCcpO1xuICB9XG5cbiAgYXN5bmMgZ2V0RGVhbHNQZXJmb3JtYW5jZShkYXlzOiBudW1iZXIgPSAzMCkge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9hbmFseXRpY3MvZGVhbHMvcGVyZm9ybWFuY2U/ZGF5cz0ke2RheXN9YCk7XG4gIH1cblxuICBhc3luYyBnZXRDbGllbnRJbnNpZ2h0cygpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KCcvYW5hbHl0aWNzL2NsaWVudHMvaW5zaWdodHMnKTtcbiAgfVxuXG4gIGFzeW5jIGdldFRlYW1Qcm9kdWN0aXZpdHkoKSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL2FuYWx5dGljcy90ZWFtL3Byb2R1Y3Rpdml0eScpO1xuICB9XG5cbiAgLy8gRG9jdW1lbnQgZW5kcG9pbnRzXG4gIGFzeW5jIGdldERvY3VtZW50cyhwYXJhbXM/OiB7XG4gICAgc2tpcD86IG51bWJlcjtcbiAgICBsaW1pdD86IG51bWJlcjtcbiAgICBkb2N1bWVudF90eXBlPzogc3RyaW5nO1xuICAgIGRlYWxfaWQ/OiBzdHJpbmc7XG4gICAgc3RhdHVzPzogc3RyaW5nO1xuICB9KSB7XG4gICAgY29uc3QgcXVlcnkgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XG4gICAgaWYgKHBhcmFtcz8uc2tpcCkgcXVlcnkuYXBwZW5kKCdza2lwJywgcGFyYW1zLnNraXAudG9TdHJpbmcoKSk7XG4gICAgaWYgKHBhcmFtcz8ubGltaXQpIHF1ZXJ5LmFwcGVuZCgnbGltaXQnLCBwYXJhbXMubGltaXQudG9TdHJpbmcoKSk7XG4gICAgaWYgKHBhcmFtcz8uZG9jdW1lbnRfdHlwZSkgcXVlcnkuYXBwZW5kKCdkb2N1bWVudF90eXBlJywgcGFyYW1zLmRvY3VtZW50X3R5cGUpO1xuICAgIGlmIChwYXJhbXM/LmRlYWxfaWQpIHF1ZXJ5LmFwcGVuZCgnZGVhbF9pZCcsIHBhcmFtcy5kZWFsX2lkKTtcbiAgICBpZiAocGFyYW1zPy5zdGF0dXMpIHF1ZXJ5LmFwcGVuZCgnc3RhdHVzJywgcGFyYW1zLnN0YXR1cyk7XG5cbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvZG9jdW1lbnRzPyR7cXVlcnkudG9TdHJpbmcoKX1gKTtcbiAgfVxuXG4gIGFzeW5jIGdldERvY3VtZW50KGlkOiBzdHJpbmcpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvZG9jdW1lbnRzLyR7aWR9YCk7XG4gIH1cblxuICBhc3luYyBjcmVhdGVEb2N1bWVudChkb2N1bWVudERhdGE6IGFueSkge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy9kb2N1bWVudHMnLCB7XG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGRvY3VtZW50RGF0YSksXG4gICAgfSk7XG4gIH1cblxuICBhc3luYyB1cGxvYWREb2N1bWVudChmaWxlOiBGaWxlLCBtZXRhZGF0YToge1xuICAgIHRpdGxlOiBzdHJpbmc7XG4gICAgZG9jdW1lbnRfdHlwZT86IHN0cmluZztcbiAgICBkZWFsX2lkPzogc3RyaW5nO1xuICAgIGNsaWVudF9pZD86IHN0cmluZztcbiAgICBpc19jb25maWRlbnRpYWw/OiBib29sZWFuO1xuICB9KSB7XG4gICAgY29uc3QgZm9ybURhdGEgPSBuZXcgRm9ybURhdGEoKTtcbiAgICBmb3JtRGF0YS5hcHBlbmQoJ2ZpbGUnLCBmaWxlKTtcbiAgICBmb3JtRGF0YS5hcHBlbmQoJ3RpdGxlJywgbWV0YWRhdGEudGl0bGUpO1xuICAgIGlmIChtZXRhZGF0YS5kb2N1bWVudF90eXBlKSBmb3JtRGF0YS5hcHBlbmQoJ2RvY3VtZW50X3R5cGUnLCBtZXRhZGF0YS5kb2N1bWVudF90eXBlKTtcbiAgICBpZiAobWV0YWRhdGEuZGVhbF9pZCkgZm9ybURhdGEuYXBwZW5kKCdkZWFsX2lkJywgbWV0YWRhdGEuZGVhbF9pZCk7XG4gICAgaWYgKG1ldGFkYXRhLmNsaWVudF9pZCkgZm9ybURhdGEuYXBwZW5kKCdjbGllbnRfaWQnLCBtZXRhZGF0YS5jbGllbnRfaWQpO1xuICAgIGlmIChtZXRhZGF0YS5pc19jb25maWRlbnRpYWwgIT09IHVuZGVmaW5lZCkgZm9ybURhdGEuYXBwZW5kKCdpc19jb25maWRlbnRpYWwnLCBtZXRhZGF0YS5pc19jb25maWRlbnRpYWwudG9TdHJpbmcoKSk7XG5cbiAgICBjb25zdCBoZWFkZXJzOiBSZWNvcmQ8c3RyaW5nLCBzdHJpbmc+ID0ge307XG4gICAgaWYgKHRoaXMudG9rZW4pIHtcbiAgICAgIGhlYWRlcnMuQXV0aG9yaXphdGlvbiA9IGBCZWFyZXIgJHt0aGlzLnRva2VufWA7XG4gICAgfVxuXG4gICAgdHJ5IHtcbiAgICAgIGNvbnN0IHJlc3BvbnNlID0gYXdhaXQgZmV0Y2goYCR7dGhpcy5iYXNlVXJsfS9kb2N1bWVudHMvdXBsb2FkYCwge1xuICAgICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgICAgaGVhZGVycyxcbiAgICAgICAgYm9keTogZm9ybURhdGEsXG4gICAgICB9KTtcblxuICAgICAgaWYgKCFyZXNwb25zZS5vaykge1xuICAgICAgICBjb25zdCBlcnJvckRhdGEgPSBhd2FpdCByZXNwb25zZS5qc29uKCkuY2F0Y2goKCkgPT4gKHt9KSk7XG4gICAgICAgIHRocm93IG5ldyBFcnJvcihlcnJvckRhdGEuZGV0YWlsIHx8IGVycm9yRGF0YS5tZXNzYWdlIHx8IGBIVFRQICR7cmVzcG9uc2Uuc3RhdHVzfWApO1xuICAgICAgfVxuXG4gICAgICBjb25zdCBkYXRhID0gYXdhaXQgcmVzcG9uc2UuanNvbigpO1xuICAgICAgcmV0dXJuIHsgZGF0YSB9O1xuICAgIH0gY2F0Y2ggKGVycm9yKSB7XG4gICAgICBjb25zb2xlLmVycm9yKCdEb2N1bWVudCB1cGxvYWQgZmFpbGVkOicsIGVycm9yKTtcbiAgICAgIHJldHVybiB7IGVycm9yOiBlcnJvciBpbnN0YW5jZW9mIEVycm9yID8gZXJyb3IubWVzc2FnZSA6ICdVbmtub3duIGVycm9yJyB9O1xuICAgIH1cbiAgfVxuXG4gIGFzeW5jIHVwZGF0ZURvY3VtZW50KGlkOiBzdHJpbmcsIGRvY3VtZW50RGF0YTogYW55KSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL2RvY3VtZW50cy8ke2lkfWAsIHtcbiAgICAgIG1ldGhvZDogJ1BVVCcsXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShkb2N1bWVudERhdGEpLFxuICAgIH0pO1xuICB9XG5cbiAgYXN5bmMgZGVsZXRlRG9jdW1lbnQoaWQ6IHN0cmluZykge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9kb2N1bWVudHMvJHtpZH1gLCB7IG1ldGhvZDogJ0RFTEVURScgfSk7XG4gIH1cblxuICAvLyBEb2N1bWVudCBBbmFseXNpcyBlbmRwb2ludHNcbiAgYXN5bmMgYW5hbHl6ZURvY3VtZW50KGlkOiBzdHJpbmcsIGFuYWx5c2lzVHlwZT86IHN0cmluZywgZm9jdXNBcmVhcz86IHN0cmluZ1tdKSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL2RvY3VtZW50cy8ke2lkfS9hbmFseXplYCwge1xuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeSh7XG4gICAgICAgIGFuYWx5c2lzX3R5cGU6IGFuYWx5c2lzVHlwZSB8fCAnZnVsbCcsXG4gICAgICAgIGZvY3VzX2FyZWFzOiBmb2N1c0FyZWFzIHx8IFsnZmluYW5jaWFsJywgJ2xlZ2FsJywgJ3Jpc2snXVxuICAgICAgfSlcbiAgICB9KTtcbiAgfVxuXG4gIGFzeW5jIGdldERvY3VtZW50QW5hbHlzaXMoaWQ6IHN0cmluZykge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9kb2N1bWVudHMvJHtpZH0vYW5hbHlzaXNgKTtcbiAgfVxuXG4gIGFzeW5jIGFzc2Vzc0RvY3VtZW50Umlzayhkb2N1bWVudElkczogc3RyaW5nW10sIGFzc2Vzc21lbnRUeXBlPzogc3RyaW5nKSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL2RvY3VtZW50cy9yaXNrLWFzc2Vzc21lbnQnLCB7XG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KHtcbiAgICAgICAgZG9jdW1lbnRfaWRzOiBkb2N1bWVudElkcyxcbiAgICAgICAgYXNzZXNzbWVudF90eXBlOiBhc3Nlc3NtZW50VHlwZSB8fCAnY29tcHJlaGVuc2l2ZSdcbiAgICAgIH0pXG4gICAgfSk7XG4gIH1cblxuICBhc3luYyBnZXRSaXNrQXNzZXNzbWVudHMocGFyYW1zPzogeyBza2lwPzogbnVtYmVyOyBsaW1pdD86IG51bWJlciB9KSB7XG4gICAgY29uc3QgcXVlcnkgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XG4gICAgaWYgKHBhcmFtcz8uc2tpcCkgcXVlcnkuYXBwZW5kKCdza2lwJywgcGFyYW1zLnNraXAudG9TdHJpbmcoKSk7XG4gICAgaWYgKHBhcmFtcz8ubGltaXQpIHF1ZXJ5LmFwcGVuZCgnbGltaXQnLCBwYXJhbXMubGltaXQudG9TdHJpbmcoKSk7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL2RvY3VtZW50cy9yaXNrLWFzc2Vzc21lbnRzPyR7cXVlcnl9YCk7XG4gIH1cblxuICBhc3luYyBjYXRlZ29yaXplRG9jdW1lbnQoaWQ6IHN0cmluZykge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9kb2N1bWVudHMvJHtpZH0vY2F0ZWdvcml6ZWApO1xuICB9XG5cbiAgYXN5bmMgZ2V0RG9jdW1lbnRBbmFseXRpY3MoKSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL2RvY3VtZW50cy9hbmFseXRpY3Mvc3RhdGlzdGljcycpO1xuICB9XG5cbiAgYXN5bmMgZ2V0SGlnaFJpc2tEb2N1bWVudHMocGFyYW1zPzogeyBza2lwPzogbnVtYmVyOyBsaW1pdD86IG51bWJlcjsgcmlza190aHJlc2hvbGQ/OiBudW1iZXIgfSkge1xuICAgIGNvbnN0IHF1ZXJ5ID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xuICAgIGlmIChwYXJhbXM/LnNraXApIHF1ZXJ5LmFwcGVuZCgnc2tpcCcsIHBhcmFtcy5za2lwLnRvU3RyaW5nKCkpO1xuICAgIGlmIChwYXJhbXM/LmxpbWl0KSBxdWVyeS5hcHBlbmQoJ2xpbWl0JywgcGFyYW1zLmxpbWl0LnRvU3RyaW5nKCkpO1xuICAgIGlmIChwYXJhbXM/LnJpc2tfdGhyZXNob2xkKSBxdWVyeS5hcHBlbmQoJ3Jpc2tfdGhyZXNob2xkJywgcGFyYW1zLnJpc2tfdGhyZXNob2xkLnRvU3RyaW5nKCkpO1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9kb2N1bWVudHMvaGlnaC1yaXNrPyR7cXVlcnl9YCk7XG4gIH1cblxuICAvLyBGaW5hbmNpYWwgTW9kZWxzIGVuZHBvaW50c1xuICBhc3luYyBnZXRGaW5hbmNpYWxNb2RlbHMocGFyYW1zPzoge1xuICAgIHNraXA/OiBudW1iZXI7XG4gICAgbGltaXQ/OiBudW1iZXI7XG4gICAgbW9kZWxfdHlwZT86IHN0cmluZztcbiAgICBkZWFsX2lkPzogc3RyaW5nO1xuICAgIHN0YXR1cz86IHN0cmluZztcbiAgfSkge1xuICAgIGNvbnN0IHF1ZXJ5ID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xuICAgIGlmIChwYXJhbXM/LnNraXApIHF1ZXJ5LmFwcGVuZCgnc2tpcCcsIHBhcmFtcy5za2lwLnRvU3RyaW5nKCkpO1xuICAgIGlmIChwYXJhbXM/LmxpbWl0KSBxdWVyeS5hcHBlbmQoJ2xpbWl0JywgcGFyYW1zLmxpbWl0LnRvU3RyaW5nKCkpO1xuICAgIGlmIChwYXJhbXM/Lm1vZGVsX3R5cGUpIHF1ZXJ5LmFwcGVuZCgnbW9kZWxfdHlwZScsIHBhcmFtcy5tb2RlbF90eXBlKTtcbiAgICBpZiAocGFyYW1zPy5kZWFsX2lkKSBxdWVyeS5hcHBlbmQoJ2RlYWxfaWQnLCBwYXJhbXMuZGVhbF9pZCk7XG4gICAgaWYgKHBhcmFtcz8uc3RhdHVzKSBxdWVyeS5hcHBlbmQoJ3N0YXR1cycsIHBhcmFtcy5zdGF0dXMpO1xuXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL2ZpbmFuY2lhbC1tb2RlbHM/JHtxdWVyeS50b1N0cmluZygpfWApO1xuICB9XG5cbiAgYXN5bmMgZ2V0RmluYW5jaWFsTW9kZWwoaWQ6IHN0cmluZykge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9maW5hbmNpYWwtbW9kZWxzLyR7aWR9YCk7XG4gIH1cblxuICBhc3luYyBjcmVhdGVGaW5hbmNpYWxNb2RlbChtb2RlbERhdGE6IGFueSkge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy9maW5hbmNpYWwtbW9kZWxzJywge1xuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShtb2RlbERhdGEpLFxuICAgIH0pO1xuICB9XG5cbiAgYXN5bmMgdXBkYXRlRmluYW5jaWFsTW9kZWwoaWQ6IHN0cmluZywgbW9kZWxEYXRhOiBhbnkpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvZmluYW5jaWFsLW1vZGVscy8ke2lkfWAsIHtcbiAgICAgIG1ldGhvZDogJ1BVVCcsXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShtb2RlbERhdGEpLFxuICAgIH0pO1xuICB9XG5cbiAgYXN5bmMgZGVsZXRlRmluYW5jaWFsTW9kZWwoaWQ6IHN0cmluZykge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9maW5hbmNpYWwtbW9kZWxzLyR7aWR9YCwgeyBtZXRob2Q6ICdERUxFVEUnIH0pO1xuICB9XG5cbiAgYXN5bmMgY3JlYXRlTW9kZWxWZXJzaW9uKGlkOiBzdHJpbmcsIHZlcnNpb25EYXRhOiBhbnkpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvZmluYW5jaWFsLW1vZGVscy8ke2lkfS92ZXJzaW9uc2AsIHtcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkodmVyc2lvbkRhdGEpLFxuICAgIH0pO1xuICB9XG5cbiAgYXN5bmMgZ2V0TW9kZWxWZXJzaW9ucyhpZDogc3RyaW5nKSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL2ZpbmFuY2lhbC1tb2RlbHMvJHtpZH0vdmVyc2lvbnNgKTtcbiAgfVxuXG4gIGFzeW5jIGdldE1vZGVsU3RhdGlzdGljcygpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KCcvZmluYW5jaWFsLW1vZGVscy9zdGF0aXN0aWNzJyk7XG4gIH1cblxuICAvLyBPcmdhbml6YXRpb24gZW5kcG9pbnRzXG4gIGFzeW5jIGdldEN1cnJlbnRPcmdhbml6YXRpb24oKSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL29yZ2FuaXphdGlvbnMvbWUnKTtcbiAgfVxuXG4gIGFzeW5jIHVwZGF0ZUN1cnJlbnRPcmdhbml6YXRpb24ob3JnRGF0YTogYW55KSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL29yZ2FuaXphdGlvbnMvbWUnLCB7XG4gICAgICBtZXRob2Q6ICdQVVQnLFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkob3JnRGF0YSksXG4gICAgfSk7XG4gIH1cblxuICBhc3luYyBnZXRPcmdhbml6YXRpb25TdGF0cygpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KCcvb3JnYW5pemF0aW9ucy9tZS9zdGF0cycpO1xuICB9XG5cbiAgLy8gUHJlc2VudGF0aW9uIGVuZHBvaW50c1xuICBhc3luYyBnZXRQcmVzZW50YXRpb25zKHBhcmFtcz86IHtcbiAgICBza2lwPzogbnVtYmVyO1xuICAgIGxpbWl0PzogbnVtYmVyO1xuICAgIHN0YXR1cz86IHN0cmluZztcbiAgICBwcmVzZW50YXRpb25fdHlwZT86IHN0cmluZztcbiAgICBkZWFsX2lkPzogc3RyaW5nO1xuICAgIGNyZWF0ZWRfYnlfbWU/OiBib29sZWFuO1xuICB9KSB7XG4gICAgY29uc3QgcXVlcnkgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XG4gICAgaWYgKHBhcmFtcz8uc2tpcCkgcXVlcnkuYXBwZW5kKCdza2lwJywgcGFyYW1zLnNraXAudG9TdHJpbmcoKSk7XG4gICAgaWYgKHBhcmFtcz8ubGltaXQpIHF1ZXJ5LmFwcGVuZCgnbGltaXQnLCBwYXJhbXMubGltaXQudG9TdHJpbmcoKSk7XG4gICAgaWYgKHBhcmFtcz8uc3RhdHVzKSBxdWVyeS5hcHBlbmQoJ3N0YXR1cycsIHBhcmFtcy5zdGF0dXMpO1xuICAgIGlmIChwYXJhbXM/LnByZXNlbnRhdGlvbl90eXBlKSBxdWVyeS5hcHBlbmQoJ3ByZXNlbnRhdGlvbl90eXBlJywgcGFyYW1zLnByZXNlbnRhdGlvbl90eXBlKTtcbiAgICBpZiAocGFyYW1zPy5kZWFsX2lkKSBxdWVyeS5hcHBlbmQoJ2RlYWxfaWQnLCBwYXJhbXMuZGVhbF9pZCk7XG4gICAgaWYgKHBhcmFtcz8uY3JlYXRlZF9ieV9tZSkgcXVlcnkuYXBwZW5kKCdjcmVhdGVkX2J5X21lJywgcGFyYW1zLmNyZWF0ZWRfYnlfbWUudG9TdHJpbmcoKSk7XG5cbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvcHJlc2VudGF0aW9ucy8/JHtxdWVyeS50b1N0cmluZygpfWApO1xuICB9XG5cbiAgYXN5bmMgZ2V0UHJlc2VudGF0aW9uKGlkOiBzdHJpbmcpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvcHJlc2VudGF0aW9ucy8ke2lkfWApO1xuICB9XG5cbiAgYXN5bmMgY3JlYXRlUHJlc2VudGF0aW9uKHByZXNlbnRhdGlvbkRhdGE6IGFueSkge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoJy9wcmVzZW50YXRpb25zLycsIHtcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocHJlc2VudGF0aW9uRGF0YSksXG4gICAgfSk7XG4gIH1cblxuICBhc3luYyB1cGRhdGVQcmVzZW50YXRpb24oaWQ6IHN0cmluZywgcHJlc2VudGF0aW9uRGF0YTogYW55KSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL3ByZXNlbnRhdGlvbnMvJHtpZH1gLCB7XG4gICAgICBtZXRob2Q6ICdQVVQnLFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocHJlc2VudGF0aW9uRGF0YSksXG4gICAgfSk7XG4gIH1cblxuICBhc3luYyBkZWxldGVQcmVzZW50YXRpb24oaWQ6IHN0cmluZykge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9wcmVzZW50YXRpb25zLyR7aWR9YCwgeyBtZXRob2Q6ICdERUxFVEUnIH0pO1xuICB9XG5cbiAgYXN5bmMgY3JlYXRlUHJlc2VudGF0aW9uVmVyc2lvbihpZDogc3RyaW5nKSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL3ByZXNlbnRhdGlvbnMvJHtpZH0vdmVyc2lvbmAsIHsgbWV0aG9kOiAnUE9TVCcgfSk7XG4gIH1cblxuICBhc3luYyBnZXRQcmVzZW50YXRpb25zQnlEZWFsKGRlYWxJZDogc3RyaW5nLCBwYXJhbXM/OiB7IHNraXA/OiBudW1iZXI7IGxpbWl0PzogbnVtYmVyIH0pIHtcbiAgICBjb25zdCBxdWVyeSA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcbiAgICBpZiAocGFyYW1zPy5za2lwKSBxdWVyeS5hcHBlbmQoJ3NraXAnLCBwYXJhbXMuc2tpcC50b1N0cmluZygpKTtcbiAgICBpZiAocGFyYW1zPy5saW1pdCkgcXVlcnkuYXBwZW5kKCdsaW1pdCcsIHBhcmFtcy5saW1pdC50b1N0cmluZygpKTtcblxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9wcmVzZW50YXRpb25zL2RlYWxzLyR7ZGVhbElkfT8ke3F1ZXJ5LnRvU3RyaW5nKCl9YCk7XG4gIH1cblxuICBhc3luYyBnZXRTaGFyZWRQcmVzZW50YXRpb25zKHBhcmFtcz86IHsgc2tpcD86IG51bWJlcjsgbGltaXQ/OiBudW1iZXIgfSkge1xuICAgIGNvbnN0IHF1ZXJ5ID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xuICAgIGlmIChwYXJhbXM/LnNraXApIHF1ZXJ5LmFwcGVuZCgnc2tpcCcsIHBhcmFtcy5za2lwLnRvU3RyaW5nKCkpO1xuICAgIGlmIChwYXJhbXM/LmxpbWl0KSBxdWVyeS5hcHBlbmQoJ2xpbWl0JywgcGFyYW1zLmxpbWl0LnRvU3RyaW5nKCkpO1xuXG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL3ByZXNlbnRhdGlvbnMvc2hhcmVkPyR7cXVlcnkudG9TdHJpbmcoKX1gKTtcbiAgfVxuXG4gIC8vIFNsaWRlIGVuZHBvaW50c1xuICBhc3luYyBnZXRQcmVzZW50YXRpb25TbGlkZXMocHJlc2VudGF0aW9uSWQ6IHN0cmluZywgcGFyYW1zPzogeyBza2lwPzogbnVtYmVyOyBsaW1pdD86IG51bWJlciB9KSB7XG4gICAgY29uc3QgcXVlcnkgPSBuZXcgVVJMU2VhcmNoUGFyYW1zKCk7XG4gICAgaWYgKHBhcmFtcz8uc2tpcCkgcXVlcnkuYXBwZW5kKCdza2lwJywgcGFyYW1zLnNraXAudG9TdHJpbmcoKSk7XG4gICAgaWYgKHBhcmFtcz8ubGltaXQpIHF1ZXJ5LmFwcGVuZCgnbGltaXQnLCBwYXJhbXMubGltaXQudG9TdHJpbmcoKSk7XG5cbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvcHJlc2VudGF0aW9ucy8ke3ByZXNlbnRhdGlvbklkfS9zbGlkZXM/JHtxdWVyeS50b1N0cmluZygpfWApO1xuICB9XG5cbiAgYXN5bmMgY3JlYXRlU2xpZGUocHJlc2VudGF0aW9uSWQ6IHN0cmluZywgc2xpZGVEYXRhOiBhbnkpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvcHJlc2VudGF0aW9ucy8ke3ByZXNlbnRhdGlvbklkfS9zbGlkZXMvYCwge1xuICAgICAgbWV0aG9kOiAnUE9TVCcsXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShzbGlkZURhdGEpLFxuICAgIH0pO1xuICB9XG5cbiAgYXN5bmMgZ2V0U2xpZGUocHJlc2VudGF0aW9uSWQ6IHN0cmluZywgc2xpZGVJZDogc3RyaW5nKSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL3ByZXNlbnRhdGlvbnMvJHtwcmVzZW50YXRpb25JZH0vc2xpZGVzLyR7c2xpZGVJZH1gKTtcbiAgfVxuXG4gIGFzeW5jIHVwZGF0ZVNsaWRlKHByZXNlbnRhdGlvbklkOiBzdHJpbmcsIHNsaWRlSWQ6IHN0cmluZywgc2xpZGVEYXRhOiBhbnkpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvcHJlc2VudGF0aW9ucy8ke3ByZXNlbnRhdGlvbklkfS9zbGlkZXMvJHtzbGlkZUlkfWAsIHtcbiAgICAgIG1ldGhvZDogJ1BVVCcsXG4gICAgICBib2R5OiBKU09OLnN0cmluZ2lmeShzbGlkZURhdGEpLFxuICAgIH0pO1xuICB9XG5cbiAgYXN5bmMgZGVsZXRlU2xpZGUocHJlc2VudGF0aW9uSWQ6IHN0cmluZywgc2xpZGVJZDogc3RyaW5nKSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL3ByZXNlbnRhdGlvbnMvJHtwcmVzZW50YXRpb25JZH0vc2xpZGVzLyR7c2xpZGVJZH1gLCB7IG1ldGhvZDogJ0RFTEVURScgfSk7XG4gIH1cblxuICBhc3luYyBkdXBsaWNhdGVTbGlkZShwcmVzZW50YXRpb25JZDogc3RyaW5nLCBzbGlkZUlkOiBzdHJpbmcsIG5ld1NsaWRlTnVtYmVyOiBudW1iZXIpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvcHJlc2VudGF0aW9ucy8ke3ByZXNlbnRhdGlvbklkfS9zbGlkZXMvJHtzbGlkZUlkfS9kdXBsaWNhdGU/bmV3X3NsaWRlX251bWJlcj0ke25ld1NsaWRlTnVtYmVyfWAsIHtcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgIH0pO1xuICB9XG5cbiAgLy8gVGVtcGxhdGUgZW5kcG9pbnRzXG4gIGFzeW5jIGdldFByZXNlbnRhdGlvblRlbXBsYXRlcyhwYXJhbXM/OiB7XG4gICAgc2tpcD86IG51bWJlcjtcbiAgICBsaW1pdD86IG51bWJlcjtcbiAgICBjYXRlZ29yeT86IHN0cmluZztcbiAgICBmZWF0dXJlZF9vbmx5PzogYm9vbGVhbjtcbiAgICBwdWJsaWNfb25seT86IGJvb2xlYW47XG4gIH0pIHtcbiAgICBjb25zdCBxdWVyeSA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcbiAgICBpZiAocGFyYW1zPy5za2lwKSBxdWVyeS5hcHBlbmQoJ3NraXAnLCBwYXJhbXMuc2tpcC50b1N0cmluZygpKTtcbiAgICBpZiAocGFyYW1zPy5saW1pdCkgcXVlcnkuYXBwZW5kKCdsaW1pdCcsIHBhcmFtcy5saW1pdC50b1N0cmluZygpKTtcbiAgICBpZiAocGFyYW1zPy5jYXRlZ29yeSkgcXVlcnkuYXBwZW5kKCdjYXRlZ29yeScsIHBhcmFtcy5jYXRlZ29yeSk7XG4gICAgaWYgKHBhcmFtcz8uZmVhdHVyZWRfb25seSkgcXVlcnkuYXBwZW5kKCdmZWF0dXJlZF9vbmx5JywgcGFyYW1zLmZlYXR1cmVkX29ubHkudG9TdHJpbmcoKSk7XG4gICAgaWYgKHBhcmFtcz8ucHVibGljX29ubHkpIHF1ZXJ5LmFwcGVuZCgncHVibGljX29ubHknLCBwYXJhbXMucHVibGljX29ubHkudG9TdHJpbmcoKSk7XG5cbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvcHJlc2VudGF0aW9ucy90ZW1wbGF0ZXMvPyR7cXVlcnkudG9TdHJpbmcoKX1gKTtcbiAgfVxuXG4gIGFzeW5jIGNyZWF0ZVByZXNlbnRhdGlvblRlbXBsYXRlKHRlbXBsYXRlRGF0YTogYW55KSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdCgnL3ByZXNlbnRhdGlvbnMvdGVtcGxhdGVzLycsIHtcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkodGVtcGxhdGVEYXRhKSxcbiAgICB9KTtcbiAgfVxuXG4gIGFzeW5jIGdldFByZXNlbnRhdGlvblRlbXBsYXRlKGlkOiBzdHJpbmcpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvcHJlc2VudGF0aW9ucy90ZW1wbGF0ZXMvJHtpZH1gKTtcbiAgfVxuXG4gIGFzeW5jIGNyZWF0ZVByZXNlbnRhdGlvbkZyb21UZW1wbGF0ZSh0ZW1wbGF0ZUlkOiBzdHJpbmcsIHByZXNlbnRhdGlvbkRhdGE6IGFueSkge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9wcmVzZW50YXRpb25zL3RlbXBsYXRlcy8ke3RlbXBsYXRlSWR9L3VzZWAsIHtcbiAgICAgIG1ldGhvZDogJ1BPU1QnLFxuICAgICAgYm9keTogSlNPTi5zdHJpbmdpZnkocHJlc2VudGF0aW9uRGF0YSksXG4gICAgfSk7XG4gIH1cblxuICAvLyBDb21tZW50IGVuZHBvaW50c1xuICBhc3luYyBnZXRQcmVzZW50YXRpb25Db21tZW50cyhwcmVzZW50YXRpb25JZDogc3RyaW5nLCBwYXJhbXM/OiB7XG4gICAgc2tpcD86IG51bWJlcjtcbiAgICBsaW1pdD86IG51bWJlcjtcbiAgICByZXNvbHZlZF9vbmx5PzogYm9vbGVhbjtcbiAgfSkge1xuICAgIGNvbnN0IHF1ZXJ5ID0gbmV3IFVSTFNlYXJjaFBhcmFtcygpO1xuICAgIGlmIChwYXJhbXM/LnNraXApIHF1ZXJ5LmFwcGVuZCgnc2tpcCcsIHBhcmFtcy5za2lwLnRvU3RyaW5nKCkpO1xuICAgIGlmIChwYXJhbXM/LmxpbWl0KSBxdWVyeS5hcHBlbmQoJ2xpbWl0JywgcGFyYW1zLmxpbWl0LnRvU3RyaW5nKCkpO1xuICAgIGlmIChwYXJhbXM/LnJlc29sdmVkX29ubHkgIT09IHVuZGVmaW5lZCkgcXVlcnkuYXBwZW5kKCdyZXNvbHZlZF9vbmx5JywgcGFyYW1zLnJlc29sdmVkX29ubHkudG9TdHJpbmcoKSk7XG5cbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvcHJlc2VudGF0aW9ucy8ke3ByZXNlbnRhdGlvbklkfS9jb21tZW50cz8ke3F1ZXJ5LnRvU3RyaW5nKCl9YCk7XG4gIH1cblxuICBhc3luYyBjcmVhdGVQcmVzZW50YXRpb25Db21tZW50KHByZXNlbnRhdGlvbklkOiBzdHJpbmcsIGNvbW1lbnREYXRhOiBhbnkpIHtcbiAgICByZXR1cm4gdGhpcy5yZXF1ZXN0KGAvcHJlc2VudGF0aW9ucy8ke3ByZXNlbnRhdGlvbklkfS9jb21tZW50cy9gLCB7XG4gICAgICBtZXRob2Q6ICdQT1NUJyxcbiAgICAgIGJvZHk6IEpTT04uc3RyaW5naWZ5KGNvbW1lbnREYXRhKSxcbiAgICB9KTtcbiAgfVxuXG4gIGFzeW5jIHJlc29sdmVQcmVzZW50YXRpb25Db21tZW50KHByZXNlbnRhdGlvbklkOiBzdHJpbmcsIGNvbW1lbnRJZDogc3RyaW5nKSB7XG4gICAgcmV0dXJuIHRoaXMucmVxdWVzdChgL3ByZXNlbnRhdGlvbnMvJHtwcmVzZW50YXRpb25JZH0vY29tbWVudHMvJHtjb21tZW50SWR9L3Jlc29sdmVgLCB7XG4gICAgICBtZXRob2Q6ICdQVVQnLFxuICAgIH0pO1xuICB9XG5cbiAgLy8gQ29sbGFib3JhdGlvbiBlbmRwb2ludHNcbiAgYXN5bmMgZ2V0UHJlc2VudGF0aW9uQWN0aXZpdGllcyhwcmVzZW50YXRpb25JZDogc3RyaW5nLCBwYXJhbXM/OiB7IHNraXA/OiBudW1iZXI7IGxpbWl0PzogbnVtYmVyIH0pIHtcbiAgICBjb25zdCBxdWVyeSA9IG5ldyBVUkxTZWFyY2hQYXJhbXMoKTtcbiAgICBpZiAocGFyYW1zPy5za2lwKSBxdWVyeS5hcHBlbmQoJ3NraXAnLCBwYXJhbXMuc2tpcC50b1N0cmluZygpKTtcbiAgICBpZiAocGFyYW1zPy5saW1pdCkgcXVlcnkuYXBwZW5kKCdsaW1pdCcsIHBhcmFtcy5saW1pdC50b1N0cmluZygpKTtcblxuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9wcmVzZW50YXRpb25zLyR7cHJlc2VudGF0aW9uSWR9L2FjdGl2aXRpZXM/JHtxdWVyeS50b1N0cmluZygpfWApO1xuICB9XG5cbiAgYXN5bmMgZ2V0UHJlc2VudGF0aW9uQWN0aXZlVXNlcnMocHJlc2VudGF0aW9uSWQ6IHN0cmluZykge1xuICAgIHJldHVybiB0aGlzLnJlcXVlc3QoYC9wcmVzZW50YXRpb25zLyR7cHJlc2VudGF0aW9uSWR9L2FjdGl2ZS11c2Vyc2ApO1xuICB9XG59XG5cbi8vIENyZWF0ZSBzaW5nbGV0b24gaW5zdGFuY2VcbmV4cG9ydCBjb25zdCBhcGlDbGllbnQgPSBuZXcgQXBpQ2xpZW50KCk7XG5cbi8vIEV4cG9ydCB0eXBlcyBmb3IgdXNlIGluIGNvbXBvbmVudHNcbmV4cG9ydCB0eXBlIHsgQXBpUmVzcG9uc2UgfTtcbiJdLCJuYW1lcyI6WyJBUElfQkFTRV9VUkwiLCJwcm9jZXNzIiwiZW52IiwiTkVYVF9QVUJMSUNfQVBJX1VSTCIsIkFwaUNsaWVudCIsInNldFRva2VuIiwidG9rZW4iLCJsb2NhbFN0b3JhZ2UiLCJzZXRJdGVtIiwiY2xlYXJUb2tlbiIsInJlbW92ZUl0ZW0iLCJyZXF1ZXN0IiwiZW5kcG9pbnQiLCJvcHRpb25zIiwidXJsIiwiYmFzZVVybCIsImhlYWRlcnMiLCJBdXRob3JpemF0aW9uIiwicmVzcG9uc2UiLCJmZXRjaCIsIm9rIiwic3RhdHVzIiwicmVmcmVzaGVkIiwicmVmcmVzaFRva2VuIiwicmV0cnlSZXNwb25zZSIsImRhdGEiLCJqc29uIiwid2luZG93IiwibG9jYXRpb24iLCJocmVmIiwiRXJyb3IiLCJlcnJvckRhdGEiLCJjYXRjaCIsImRldGFpbCIsIm1lc3NhZ2UiLCJlcnJvciIsImNvbnNvbGUiLCJUeXBlRXJyb3IiLCJpbmNsdWRlcyIsImdldEl0ZW0iLCJtZXRob2QiLCJib2R5IiwiSlNPTiIsInN0cmluZ2lmeSIsInJlZnJlc2hfdG9rZW4iLCJhY2Nlc3NfdG9rZW4iLCJsb2dpbiIsImVtYWlsIiwicGFzc3dvcmQiLCJyZWdpc3RlciIsInVzZXJEYXRhIiwibG9nb3V0IiwicmVzdWx0IiwiZ2V0Q3VycmVudFVzZXIiLCJ1cGRhdGVDdXJyZW50VXNlciIsImdldFVzZXJzIiwicGFyYW1zIiwicXVlcnkiLCJVUkxTZWFyY2hQYXJhbXMiLCJza2lwIiwiYXBwZW5kIiwidG9TdHJpbmciLCJsaW1pdCIsImdldERlYWxzIiwic3RhZ2UiLCJnZXREZWFsIiwiaWQiLCJjcmVhdGVEZWFsIiwiZGVhbERhdGEiLCJ1cGRhdGVEZWFsIiwiZGVsZXRlRGVhbCIsImdldERlYWxzU3RhdHMiLCJnZXRDbGllbnRzIiwiY2xpZW50X3R5cGUiLCJpbmR1c3RyeSIsInNlYXJjaCIsImdldENsaWVudCIsImNyZWF0ZUNsaWVudCIsImNsaWVudERhdGEiLCJ1cGRhdGVDbGllbnQiLCJkZWxldGVDbGllbnQiLCJnZXREYXNoYm9hcmRBbmFseXRpY3MiLCJnZXREZWFsc1BlcmZvcm1hbmNlIiwiZGF5cyIsImdldENsaWVudEluc2lnaHRzIiwiZ2V0VGVhbVByb2R1Y3Rpdml0eSIsImdldERvY3VtZW50cyIsImRvY3VtZW50X3R5cGUiLCJkZWFsX2lkIiwiZ2V0RG9jdW1lbnQiLCJjcmVhdGVEb2N1bWVudCIsImRvY3VtZW50RGF0YSIsInVwbG9hZERvY3VtZW50IiwiZmlsZSIsIm1ldGFkYXRhIiwiZm9ybURhdGEiLCJGb3JtRGF0YSIsInRpdGxlIiwiY2xpZW50X2lkIiwiaXNfY29uZmlkZW50aWFsIiwidW5kZWZpbmVkIiwidXBkYXRlRG9jdW1lbnQiLCJkZWxldGVEb2N1bWVudCIsImFuYWx5emVEb2N1bWVudCIsImFuYWx5c2lzVHlwZSIsImZvY3VzQXJlYXMiLCJhbmFseXNpc190eXBlIiwiZm9jdXNfYXJlYXMiLCJnZXREb2N1bWVudEFuYWx5c2lzIiwiYXNzZXNzRG9jdW1lbnRSaXNrIiwiZG9jdW1lbnRJZHMiLCJhc3Nlc3NtZW50VHlwZSIsImRvY3VtZW50X2lkcyIsImFzc2Vzc21lbnRfdHlwZSIsImdldFJpc2tBc3Nlc3NtZW50cyIsImNhdGVnb3JpemVEb2N1bWVudCIsImdldERvY3VtZW50QW5hbHl0aWNzIiwiZ2V0SGlnaFJpc2tEb2N1bWVudHMiLCJyaXNrX3RocmVzaG9sZCIsImdldEZpbmFuY2lhbE1vZGVscyIsIm1vZGVsX3R5cGUiLCJnZXRGaW5hbmNpYWxNb2RlbCIsImNyZWF0ZUZpbmFuY2lhbE1vZGVsIiwibW9kZWxEYXRhIiwidXBkYXRlRmluYW5jaWFsTW9kZWwiLCJkZWxldGVGaW5hbmNpYWxNb2RlbCIsImNyZWF0ZU1vZGVsVmVyc2lvbiIsInZlcnNpb25EYXRhIiwiZ2V0TW9kZWxWZXJzaW9ucyIsImdldE1vZGVsU3RhdGlzdGljcyIsImdldEN1cnJlbnRPcmdhbml6YXRpb24iLCJ1cGRhdGVDdXJyZW50T3JnYW5pemF0aW9uIiwib3JnRGF0YSIsImdldE9yZ2FuaXphdGlvblN0YXRzIiwiZ2V0UHJlc2VudGF0aW9ucyIsInByZXNlbnRhdGlvbl90eXBlIiwiY3JlYXRlZF9ieV9tZSIsImdldFByZXNlbnRhdGlvbiIsImNyZWF0ZVByZXNlbnRhdGlvbiIsInByZXNlbnRhdGlvbkRhdGEiLCJ1cGRhdGVQcmVzZW50YXRpb24iLCJkZWxldGVQcmVzZW50YXRpb24iLCJjcmVhdGVQcmVzZW50YXRpb25WZXJzaW9uIiwiZ2V0UHJlc2VudGF0aW9uc0J5RGVhbCIsImRlYWxJZCIsImdldFNoYXJlZFByZXNlbnRhdGlvbnMiLCJnZXRQcmVzZW50YXRpb25TbGlkZXMiLCJwcmVzZW50YXRpb25JZCIsImNyZWF0ZVNsaWRlIiwic2xpZGVEYXRhIiwiZ2V0U2xpZGUiLCJzbGlkZUlkIiwidXBkYXRlU2xpZGUiLCJkZWxldGVTbGlkZSIsImR1cGxpY2F0ZVNsaWRlIiwibmV3U2xpZGVOdW1iZXIiLCJnZXRQcmVzZW50YXRpb25UZW1wbGF0ZXMiLCJjYXRlZ29yeSIsImZlYXR1cmVkX29ubHkiLCJwdWJsaWNfb25seSIsImNyZWF0ZVByZXNlbnRhdGlvblRlbXBsYXRlIiwidGVtcGxhdGVEYXRhIiwiZ2V0UHJlc2VudGF0aW9uVGVtcGxhdGUiLCJjcmVhdGVQcmVzZW50YXRpb25Gcm9tVGVtcGxhdGUiLCJ0ZW1wbGF0ZUlkIiwiZ2V0UHJlc2VudGF0aW9uQ29tbWVudHMiLCJyZXNvbHZlZF9vbmx5IiwiY3JlYXRlUHJlc2VudGF0aW9uQ29tbWVudCIsImNvbW1lbnREYXRhIiwicmVzb2x2ZVByZXNlbnRhdGlvbkNvbW1lbnQiLCJjb21tZW50SWQiLCJnZXRQcmVzZW50YXRpb25BY3Rpdml0aWVzIiwiZ2V0UHJlc2VudGF0aW9uQWN0aXZlVXNlcnMiLCJjb25zdHJ1Y3RvciIsImFwaUNsaWVudCJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/api-client.ts\n"));

/***/ }),

/***/ "(app-pages-browser)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ (function(module, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: function() { return /* binding */ AuthProvider; },\n/* harmony export */   useAuth: function() { return /* binding */ useAuth; },\n/* harmony export */   withAuth: function() { return /* binding */ withAuth; }\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./api-client */ \"(app-pages-browser)/./lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth auto */ \nvar _s = $RefreshSig$(), _s1 = $RefreshSig$();\n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider(param) {\n    let { children } = param;\n    _s();\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user is already logged in\n        const token = localStorage.getItem(\"access_token\");\n        if (token) {\n            _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.setToken(token);\n            refreshUser();\n        } else {\n            setLoading(false);\n        }\n    }, []);\n    const refreshUser = async ()=>{\n        try {\n            const response = await _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.getCurrentUser();\n            if (response.data) {\n                setUser(response.data);\n            } else {\n                // Token is invalid, clear it\n                _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.clearToken();\n                setUser(null);\n            }\n        } catch (error) {\n            console.error(\"Failed to refresh user:\", error);\n            _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.clearToken();\n            setUser(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            const response = await _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.login(email, password);\n            if (response.data) {\n                const { access_token, refresh_token } = response.data;\n                // Store tokens\n                _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.setToken(access_token);\n                localStorage.setItem(\"refresh_token\", refresh_token);\n                // Get user data\n                await refreshUser();\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: response.error || \"Login failed\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : \"Login failed\"\n            };\n        }\n    };\n    const logout = ()=>{\n        _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.logout();\n        setUser(null);\n        // Redirect to login page\n        window.location.href = \"/auth/login\";\n    };\n    const value = {\n        user,\n        loading,\n        login,\n        logout,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\lib\\\\auth-context.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\n_s(AuthProvider, \"NiO5z6JIqzX62LS5UWDgIqbZYyY=\");\n_c = AuthProvider;\nfunction useAuth() {\n    _s1();\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n_s1(useAuth, \"b9L3QQ+jgeyIrH0NfHrJ8nn7VMU=\");\n// Higher-order component for protecting routes\nfunction withAuth(Component) {\n    var _s = $RefreshSig$();\n    return _s(function AuthenticatedComponent(props) {\n        _s();\n        const { user, loading } = useAuth();\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\lib\\\\auth-context.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\lib\\\\auth-context.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this);\n        }\n        if (!user) {\n            // Redirect to login\n            if (true) {\n                window.location.href = \"/auth/login\";\n            }\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\lib\\\\auth-context.tsx\",\n            lineNumber: 141,\n            columnNumber: 12\n        }, this);\n    }, \"EmJkapf7qiLC5Br5eCoEq4veZes=\", false, function() {\n        return [\n            useAuth\n        ];\n    });\n}\nvar _c;\n$RefreshReg$(_c, \"AuthProvider\");\n\n\n;\n    // Wrapped in an IIFE to avoid polluting the global scope\n    ;\n    (function () {\n        var _a, _b;\n        // Legacy CSS implementations will `eval` browser code in a Node.js context\n        // to extract CSS. For backwards compatibility, we need to check we're in a\n        // browser context before continuing.\n        if (typeof self !== 'undefined' &&\n            // AMP / No-JS mode does not inject these helpers:\n            '$RefreshHelpers$' in self) {\n            // @ts-ignore __webpack_module__ is global\n            var currentExports = module.exports;\n            // @ts-ignore __webpack_module__ is global\n            var prevSignature = (_b = (_a = module.hot.data) === null || _a === void 0 ? void 0 : _a.prevSignature) !== null && _b !== void 0 ? _b : null;\n            // This cannot happen in MainTemplate because the exports mismatch between\n            // templating and execution.\n            self.$RefreshHelpers$.registerExportsForReactRefresh(currentExports, module.id);\n            // A module can be accepted automatically based on its exports, e.g. when\n            // it is a Refresh Boundary.\n            if (self.$RefreshHelpers$.isReactRefreshBoundary(currentExports)) {\n                // Save the previous exports signature on update so we can compare the boundary\n                // signatures. We avoid saving exports themselves since it causes memory leaks (https://github.com/vercel/next.js/pull/53797)\n                module.hot.dispose(function (data) {\n                    data.prevSignature =\n                        self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports);\n                });\n                // Unconditionally accept an update to this module, we'll check if it's\n                // still a Refresh Boundary later.\n                // @ts-ignore importMeta is replaced in the loader\n                module.hot.accept();\n                // This field is set when the previous version of this module was a\n                // Refresh Boundary, letting us know we need to check for invalidation or\n                // enqueue an update.\n                if (prevSignature !== null) {\n                    // A boundary can become ineligible if its exports are incompatible\n                    // with the previous exports.\n                    //\n                    // For example, if you add/remove/change exports, we'll want to\n                    // re-execute the importing modules, and force those components to\n                    // re-render. Similarly, if you convert a class component to a\n                    // function, we want to invalidate the boundary.\n                    if (self.$RefreshHelpers$.shouldInvalidateReactRefreshBoundary(prevSignature, self.$RefreshHelpers$.getRefreshBoundarySignature(currentExports))) {\n                        module.hot.invalidate();\n                    }\n                    else {\n                        self.$RefreshHelpers$.scheduleUpdate();\n                    }\n                }\n            }\n            else {\n                // Since we just executed the code for the module, it's possible that the\n                // new exports made it ineligible for being a boundary.\n                // We only care about the case when we were _previously_ a boundary,\n                // because we already accepted this update (accidental side effect).\n                var isNoLongerABoundary = prevSignature !== null;\n                if (isNoLongerABoundary) {\n                    module.hot.invalidate();\n                }\n            }\n        }\n    })();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./lib/auth-context.tsx\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}":
/*!****************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"Inter","arguments":[{"subsets":["latin"],"display":"swap","variable":"--font-inter"}],"variableName":"inter"} ***!
  \****************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__Inter_e8ce0c', '__Inter_Fallback_e8ce0c'\",\"fontStyle\":\"normal\"},\"className\":\"__className_e8ce0c\",\"variable\":\"__variable_e8ce0c\"};\n    if(true) {\n      // 1751883368500\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwiYXBwXFxcXGxheW91dC50c3hcIixcImltcG9ydFwiOlwiSW50ZXJcIixcImFyZ3VtZW50c1wiOlt7XCJzdWJzZXRzXCI6W1wibGF0aW5cIl0sXCJkaXNwbGF5XCI6XCJzd2FwXCIsXCJ2YXJpYWJsZVwiOlwiLS1mb250LWludGVyXCJ9XSxcInZhcmlhYmxlTmFtZVwiOlwiaW50ZXJcIn0iLCJtYXBwaW5ncyI6IkFBQUE7QUFDQSxrQkFBa0IsU0FBUyxnRkFBZ0Y7QUFDM0csT0FBTyxJQUFVO0FBQ2pCO0FBQ0Esc0JBQXNCLG1CQUFPLENBQUMsd01BQXVMLGNBQWMsc0RBQXNEO0FBQ3pSLE1BQU0sVUFBVTtBQUNoQjtBQUNBO0FBQ0EiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZm9udC9nb29nbGUvdGFyZ2V0LmNzcz8xOTg1Il0sInNvdXJjZXNDb250ZW50IjpbIi8vIGV4dHJhY3RlZCBieSBtaW5pLWNzcy1leHRyYWN0LXBsdWdpblxubW9kdWxlLmV4cG9ydHMgPSB7XCJzdHlsZVwiOntcImZvbnRGYW1pbHlcIjpcIidfX0ludGVyX2U4Y2UwYycsICdfX0ludGVyX0ZhbGxiYWNrX2U4Y2UwYydcIixcImZvbnRTdHlsZVwiOlwibm9ybWFsXCJ9LFwiY2xhc3NOYW1lXCI6XCJfX2NsYXNzTmFtZV9lOGNlMGNcIixcInZhcmlhYmxlXCI6XCJfX3ZhcmlhYmxlX2U4Y2UwY1wifTtcbiAgICBpZihtb2R1bGUuaG90KSB7XG4gICAgICAvLyAxNzUxODgzMzY4NTAwXG4gICAgICB2YXIgY3NzUmVsb2FkID0gcmVxdWlyZShcIkU6L1RlbXBvL01pY3JvIFNhYXMgUmVzZWFyY2gvUmVzZWFyY2hlcyBvbiBNaWNybyBTQUFTIE9wcG9ydHVuaXRpZXMvRGVhbFZlcnNlIE9TL0RlYWxWZXJzZSBPUyBBcHAvbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9taW5pLWNzcy1leHRyYWN0LXBsdWdpbi9obXIvaG90TW9kdWxlUmVwbGFjZW1lbnQuanNcIikobW9kdWxlLmlkLCB7XCJwdWJsaWNQYXRoXCI6XCIvX25leHQvXCIsXCJlc01vZHVsZVwiOmZhbHNlLFwibG9jYWxzXCI6dHJ1ZX0pO1xuICAgICAgbW9kdWxlLmhvdC5kaXNwb3NlKGNzc1JlbG9hZCk7XG4gICAgICBcbiAgICB9XG4gICJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-jetbrains-mono\"}],\"variableName\":\"jetbrainsMono\"}":
/*!******************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/font/google/target.css?{"path":"app\\layout.tsx","import":"JetBrains_Mono","arguments":[{"subsets":["latin"],"display":"swap","variable":"--font-jetbrains-mono"}],"variableName":"jetbrainsMono"} ***!
  \******************************************************************************************************************************************************************************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

eval(__webpack_require__.ts("// extracted by mini-css-extract-plugin\nmodule.exports = {\"style\":{\"fontFamily\":\"'__JetBrains_Mono_3c557b', '__JetBrains_Mono_Fallback_3c557b'\",\"fontStyle\":\"normal\"},\"className\":\"__className_3c557b\",\"variable\":\"__variable_3c557b\"};\n    if(true) {\n      // 1751883368503\n      var cssReload = __webpack_require__(/*! ./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/mini-css-extract-plugin/hmr/hotModuleReplacement.js\")(module.id, {\"publicPath\":\"/_next/\",\"esModule\":false,\"locals\":true});\n      module.hot.dispose(cssReload);\n      \n    }\n  //# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2ZvbnQvZ29vZ2xlL3RhcmdldC5jc3M/e1wicGF0aFwiOlwiYXBwXFxcXGxheW91dC50c3hcIixcImltcG9ydFwiOlwiSmV0QnJhaW5zX01vbm9cIixcImFyZ3VtZW50c1wiOlt7XCJzdWJzZXRzXCI6W1wibGF0aW5cIl0sXCJkaXNwbGF5XCI6XCJzd2FwXCIsXCJ2YXJpYWJsZVwiOlwiLS1mb250LWpldGJyYWlucy1tb25vXCJ9XSxcInZhcmlhYmxlTmFtZVwiOlwiamV0YnJhaW5zTW9ub1wifSIsIm1hcHBpbmdzIjoiQUFBQTtBQUNBLGtCQUFrQixTQUFTLGtHQUFrRztBQUM3SCxPQUFPLElBQVU7QUFDakI7QUFDQSxzQkFBc0IsbUJBQU8sQ0FBQyx3TUFBdUwsY0FBYyxzREFBc0Q7QUFDelIsTUFBTSxVQUFVO0FBQ2hCO0FBQ0E7QUFDQSIsInNvdXJjZXMiOlsid2VicGFjazovL19OX0UvLi9ub2RlX21vZHVsZXMvbmV4dC9mb250L2dvb2dsZS90YXJnZXQuY3NzP2I3OWYiXSwic291cmNlc0NvbnRlbnQiOlsiLy8gZXh0cmFjdGVkIGJ5IG1pbmktY3NzLWV4dHJhY3QtcGx1Z2luXG5tb2R1bGUuZXhwb3J0cyA9IHtcInN0eWxlXCI6e1wiZm9udEZhbWlseVwiOlwiJ19fSmV0QnJhaW5zX01vbm9fM2M1NTdiJywgJ19fSmV0QnJhaW5zX01vbm9fRmFsbGJhY2tfM2M1NTdiJ1wiLFwiZm9udFN0eWxlXCI6XCJub3JtYWxcIn0sXCJjbGFzc05hbWVcIjpcIl9fY2xhc3NOYW1lXzNjNTU3YlwiLFwidmFyaWFibGVcIjpcIl9fdmFyaWFibGVfM2M1NTdiXCJ9O1xuICAgIGlmKG1vZHVsZS5ob3QpIHtcbiAgICAgIC8vIDE3NTE4ODMzNjg1MDNcbiAgICAgIHZhciBjc3NSZWxvYWQgPSByZXF1aXJlKFwiRTovVGVtcG8vTWljcm8gU2FhcyBSZXNlYXJjaC9SZXNlYXJjaGVzIG9uIE1pY3JvIFNBQVMgT3Bwb3J0dW5pdGllcy9EZWFsVmVyc2UgT1MvRGVhbFZlcnNlIE9TIEFwcC9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2NvbXBpbGVkL21pbmktY3NzLWV4dHJhY3QtcGx1Z2luL2htci9ob3RNb2R1bGVSZXBsYWNlbWVudC5qc1wiKShtb2R1bGUuaWQsIHtcInB1YmxpY1BhdGhcIjpcIi9fbmV4dC9cIixcImVzTW9kdWxlXCI6ZmFsc2UsXCJsb2NhbHNcIjp0cnVlfSk7XG4gICAgICBtb2R1bGUuaG90LmRpc3Bvc2UoY3NzUmVsb2FkKTtcbiAgICAgIFxuICAgIH1cbiAgIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-jetbrains-mono\"}],\"variableName\":\"jetbrainsMono\"}\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js":
/*!****************************************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js ***!
  \****************************************************************************************/
/***/ (function(__unused_webpack_module, exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("/**\n * @license React\n * react-jsx-dev-runtime.development.js\n *\n * Copyright (c) Meta Platforms, Inc. and affiliates.\n *\n * This source code is licensed under the MIT license found in the\n * LICENSE file in the root directory of this source tree.\n */\n\n\n\nif (true) {\n  (function() {\n'use strict';\n\nvar React = __webpack_require__(/*! next/dist/compiled/react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n\n// ATTENTION\n// When adding new symbols to this file,\n// Please consider also adding to 'react-devtools-shared/src/backend/ReactSymbols'\n// The Symbol used to tag the ReactElement-like types.\nvar REACT_ELEMENT_TYPE = Symbol.for('react.element');\nvar REACT_PORTAL_TYPE = Symbol.for('react.portal');\nvar REACT_FRAGMENT_TYPE = Symbol.for('react.fragment');\nvar REACT_STRICT_MODE_TYPE = Symbol.for('react.strict_mode');\nvar REACT_PROFILER_TYPE = Symbol.for('react.profiler');\nvar REACT_PROVIDER_TYPE = Symbol.for('react.provider'); // TODO: Delete with enableRenderableContext\n\nvar REACT_CONSUMER_TYPE = Symbol.for('react.consumer');\nvar REACT_CONTEXT_TYPE = Symbol.for('react.context');\nvar REACT_FORWARD_REF_TYPE = Symbol.for('react.forward_ref');\nvar REACT_SUSPENSE_TYPE = Symbol.for('react.suspense');\nvar REACT_SUSPENSE_LIST_TYPE = Symbol.for('react.suspense_list');\nvar REACT_MEMO_TYPE = Symbol.for('react.memo');\nvar REACT_LAZY_TYPE = Symbol.for('react.lazy');\nvar REACT_OFFSCREEN_TYPE = Symbol.for('react.offscreen');\nvar REACT_CACHE_TYPE = Symbol.for('react.cache');\nvar MAYBE_ITERATOR_SYMBOL = Symbol.iterator;\nvar FAUX_ITERATOR_SYMBOL = '@@iterator';\nfunction getIteratorFn(maybeIterable) {\n  if (maybeIterable === null || typeof maybeIterable !== 'object') {\n    return null;\n  }\n\n  var maybeIterator = MAYBE_ITERATOR_SYMBOL && maybeIterable[MAYBE_ITERATOR_SYMBOL] || maybeIterable[FAUX_ITERATOR_SYMBOL];\n\n  if (typeof maybeIterator === 'function') {\n    return maybeIterator;\n  }\n\n  return null;\n}\n\nvar ReactSharedInternals = React.__SECRET_INTERNALS_DO_NOT_USE_OR_YOU_WILL_BE_FIRED;\n\nfunction error(format) {\n  {\n    {\n      for (var _len2 = arguments.length, args = new Array(_len2 > 1 ? _len2 - 1 : 0), _key2 = 1; _key2 < _len2; _key2++) {\n        args[_key2 - 1] = arguments[_key2];\n      }\n\n      printWarning('error', format, args);\n    }\n  }\n}\n\nfunction printWarning(level, format, args) {\n  // When changing this logic, you might want to also\n  // update consoleWithStackDev.www.js as well.\n  {\n    var ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\n    var stack = ReactDebugCurrentFrame.getStackAddendum();\n\n    if (stack !== '') {\n      format += '%s';\n      args = args.concat([stack]);\n    } // eslint-disable-next-line react-internal/safe-string-coercion\n\n\n    var argsWithFormat = args.map(function (item) {\n      return String(item);\n    }); // Careful: RN currently depends on this prefix\n\n    argsWithFormat.unshift('Warning: ' + format); // We intentionally don't use spread (or .apply) directly because it\n    // breaks IE9: https://github.com/facebook/react/issues/13610\n    // eslint-disable-next-line react-internal/no-production-logging\n\n    Function.prototype.apply.call(console[level], console, argsWithFormat);\n  }\n}\n\n// -----------------------------------------------------------------------------\n\nvar enableScopeAPI = false; // Experimental Create Event Handle API.\nvar enableCacheElement = false;\nvar enableTransitionTracing = false; // No known bugs, but needs performance testing\n\nvar enableLegacyHidden = false; // Enables unstable_avoidThisFallback feature in Fiber\nvar enableRenderableContext = false;\n// stuff. Intended to enable React core members to more easily debug scheduling\n// issues in DEV builds.\n\nvar enableDebugTracing = false;\n\nfunction getWrappedName(outerType, innerType, wrapperName) {\n  var displayName = outerType.displayName;\n\n  if (displayName) {\n    return displayName;\n  }\n\n  var functionName = innerType.displayName || innerType.name || '';\n  return functionName !== '' ? wrapperName + \"(\" + functionName + \")\" : wrapperName;\n} // Keep in sync with react-reconciler/getComponentNameFromFiber\n\n\nfunction getContextName(type) {\n  return type.displayName || 'Context';\n}\n\nvar REACT_CLIENT_REFERENCE$2 = Symbol.for('react.client.reference'); // Note that the reconciler package should generally prefer to use getComponentNameFromFiber() instead.\n\nfunction getComponentNameFromType(type) {\n  if (type == null) {\n    // Host root, text node or just invalid type.\n    return null;\n  }\n\n  if (typeof type === 'function') {\n    if (type.$$typeof === REACT_CLIENT_REFERENCE$2) {\n      // TODO: Create a convention for naming client references with debug info.\n      return null;\n    }\n\n    return type.displayName || type.name || null;\n  }\n\n  if (typeof type === 'string') {\n    return type;\n  }\n\n  switch (type) {\n    case REACT_FRAGMENT_TYPE:\n      return 'Fragment';\n\n    case REACT_PORTAL_TYPE:\n      return 'Portal';\n\n    case REACT_PROFILER_TYPE:\n      return 'Profiler';\n\n    case REACT_STRICT_MODE_TYPE:\n      return 'StrictMode';\n\n    case REACT_SUSPENSE_TYPE:\n      return 'Suspense';\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return 'SuspenseList';\n\n    case REACT_CACHE_TYPE:\n      {\n        return 'Cache';\n      }\n\n  }\n\n  if (typeof type === 'object') {\n    {\n      if (typeof type.tag === 'number') {\n        error('Received an unexpected object in getComponentNameFromType(). ' + 'This is likely a bug in React. Please file an issue.');\n      }\n    }\n\n    switch (type.$$typeof) {\n      case REACT_PROVIDER_TYPE:\n        {\n          var provider = type;\n          return getContextName(provider._context) + '.Provider';\n        }\n\n      case REACT_CONTEXT_TYPE:\n        var context = type;\n\n        {\n          return getContextName(context) + '.Consumer';\n        }\n\n      case REACT_CONSUMER_TYPE:\n        {\n          return null;\n        }\n\n      case REACT_FORWARD_REF_TYPE:\n        return getWrappedName(type, type.render, 'ForwardRef');\n\n      case REACT_MEMO_TYPE:\n        var outerName = type.displayName || null;\n\n        if (outerName !== null) {\n          return outerName;\n        }\n\n        return getComponentNameFromType(type.type) || 'Memo';\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            return getComponentNameFromType(init(payload));\n          } catch (x) {\n            return null;\n          }\n        }\n    }\n  }\n\n  return null;\n}\n\n// $FlowFixMe[method-unbinding]\nvar hasOwnProperty = Object.prototype.hasOwnProperty;\n\nvar assign = Object.assign;\n\n/*\n * The `'' + value` pattern (used in perf-sensitive code) throws for Symbol\n * and Temporal.* types. See https://github.com/facebook/react/pull/22064.\n *\n * The functions in this module will throw an easier-to-understand,\n * easier-to-debug exception with a clear errors message message explaining the\n * problem. (Instead of a confusing exception thrown inside the implementation\n * of the `value` object).\n */\n// $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\nfunction typeName(value) {\n  {\n    // toStringTag is needed for namespaced types like Temporal.Instant\n    var hasToStringTag = typeof Symbol === 'function' && Symbol.toStringTag;\n    var type = hasToStringTag && value[Symbol.toStringTag] || value.constructor.name || 'Object'; // $FlowFixMe[incompatible-return]\n\n    return type;\n  }\n} // $FlowFixMe[incompatible-return] only called in DEV, so void return is not possible.\n\n\nfunction willCoercionThrow(value) {\n  {\n    try {\n      testStringCoercion(value);\n      return false;\n    } catch (e) {\n      return true;\n    }\n  }\n}\n\nfunction testStringCoercion(value) {\n  // If you ended up here by following an exception call stack, here's what's\n  // happened: you supplied an object or symbol value to React (as a prop, key,\n  // DOM attribute, CSS property, string ref, etc.) and when React tried to\n  // coerce it to a string using `'' + value`, an exception was thrown.\n  //\n  // The most common types that will cause this exception are `Symbol` instances\n  // and Temporal objects like `Temporal.Instant`. But any object that has a\n  // `valueOf` or `[Symbol.toPrimitive]` method that throws will also cause this\n  // exception. (Library authors do this to prevent users from using built-in\n  // numeric operators like `+` or comparison operators like `>=` because custom\n  // methods are needed to perform accurate arithmetic or comparison.)\n  //\n  // To fix the problem, coerce this object or symbol value to a string before\n  // passing it to React. The most reliable way is usually `String(value)`.\n  //\n  // To find which value is throwing, check the browser or debugger console.\n  // Before this exception was thrown, there should be `console.error` output\n  // that shows the type (Symbol, Temporal.PlainDate, etc.) that caused the\n  // problem and how that type was used: key, atrribute, input value prop, etc.\n  // In most cases, this console output also shows the component and its\n  // ancestor components where the exception happened.\n  //\n  // eslint-disable-next-line react-internal/safe-string-coercion\n  return '' + value;\n}\nfunction checkKeyStringCoercion(value) {\n  {\n    if (willCoercionThrow(value)) {\n      error('The provided key is an unsupported type %s.' + ' This value must be coerced to a string before using it here.', typeName(value));\n\n      return testStringCoercion(value); // throw (to help callers find troubleshooting comments)\n    }\n  }\n}\n\nvar REACT_CLIENT_REFERENCE$1 = Symbol.for('react.client.reference');\nfunction isValidElementType(type) {\n  if (typeof type === 'string' || typeof type === 'function') {\n    return true;\n  } // Note: typeof might be other than 'symbol' or 'number' (e.g. if it's a polyfill).\n\n\n  if (type === REACT_FRAGMENT_TYPE || type === REACT_PROFILER_TYPE || enableDebugTracing  || type === REACT_STRICT_MODE_TYPE || type === REACT_SUSPENSE_TYPE || type === REACT_SUSPENSE_LIST_TYPE || enableLegacyHidden  || type === REACT_OFFSCREEN_TYPE || enableScopeAPI  || enableCacheElement  || enableTransitionTracing ) {\n    return true;\n  }\n\n  if (typeof type === 'object' && type !== null) {\n    if (type.$$typeof === REACT_LAZY_TYPE || type.$$typeof === REACT_MEMO_TYPE || type.$$typeof === REACT_CONTEXT_TYPE || type.$$typeof === REACT_PROVIDER_TYPE || enableRenderableContext  || type.$$typeof === REACT_FORWARD_REF_TYPE || // This needs to include all possible module reference object\n    // types supported by any Flight configuration anywhere since\n    // we don't know which Flight build this will end up being used\n    // with.\n    type.$$typeof === REACT_CLIENT_REFERENCE$1 || type.getModuleId !== undefined) {\n      return true;\n    }\n  }\n\n  return false;\n}\n\nvar isArrayImpl = Array.isArray; // eslint-disable-next-line no-redeclare\n\nfunction isArray(a) {\n  return isArrayImpl(a);\n}\n\n// Helpers to patch console.logs to avoid logging during side-effect free\n// replaying on render function. This currently only patches the object\n// lazily which won't cover if the log function was extracted eagerly.\n// We could also eagerly patch the method.\nvar disabledDepth = 0;\nvar prevLog;\nvar prevInfo;\nvar prevWarn;\nvar prevError;\nvar prevGroup;\nvar prevGroupCollapsed;\nvar prevGroupEnd;\n\nfunction disabledLog() {}\n\ndisabledLog.__reactDisabledLog = true;\nfunction disableLogs() {\n  {\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      prevLog = console.log;\n      prevInfo = console.info;\n      prevWarn = console.warn;\n      prevError = console.error;\n      prevGroup = console.group;\n      prevGroupCollapsed = console.groupCollapsed;\n      prevGroupEnd = console.groupEnd; // https://github.com/facebook/react/issues/19099\n\n      var props = {\n        configurable: true,\n        enumerable: true,\n        value: disabledLog,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        info: props,\n        log: props,\n        warn: props,\n        error: props,\n        group: props,\n        groupCollapsed: props,\n        groupEnd: props\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    disabledDepth++;\n  }\n}\nfunction reenableLogs() {\n  {\n    disabledDepth--;\n\n    if (disabledDepth === 0) {\n      /* eslint-disable react-internal/no-production-logging */\n      var props = {\n        configurable: true,\n        enumerable: true,\n        writable: true\n      }; // $FlowFixMe[cannot-write] Flow thinks console is immutable.\n\n      Object.defineProperties(console, {\n        log: assign({}, props, {\n          value: prevLog\n        }),\n        info: assign({}, props, {\n          value: prevInfo\n        }),\n        warn: assign({}, props, {\n          value: prevWarn\n        }),\n        error: assign({}, props, {\n          value: prevError\n        }),\n        group: assign({}, props, {\n          value: prevGroup\n        }),\n        groupCollapsed: assign({}, props, {\n          value: prevGroupCollapsed\n        }),\n        groupEnd: assign({}, props, {\n          value: prevGroupEnd\n        })\n      });\n      /* eslint-enable react-internal/no-production-logging */\n    }\n\n    if (disabledDepth < 0) {\n      error('disabledDepth fell below zero. ' + 'This is a bug in React. Please file an issue.');\n    }\n  }\n}\n\nvar ReactCurrentDispatcher = ReactSharedInternals.ReactCurrentDispatcher;\nvar prefix;\nfunction describeBuiltInComponentFrame(name, ownerFn) {\n  {\n    if (prefix === undefined) {\n      // Extract the VM specific prefix used by each line.\n      try {\n        throw Error();\n      } catch (x) {\n        var match = x.stack.trim().match(/\\n( *(at )?)/);\n        prefix = match && match[1] || '';\n      }\n    } // We use the prefix to ensure our stacks line up with native stack frames.\n\n\n    return '\\n' + prefix + name;\n  }\n}\nvar reentry = false;\nvar componentFrameCache;\n\n{\n  var PossiblyWeakMap = typeof WeakMap === 'function' ? WeakMap : Map;\n  componentFrameCache = new PossiblyWeakMap();\n}\n/**\n * Leverages native browser/VM stack frames to get proper details (e.g.\n * filename, line + col number) for a single component in a component stack. We\n * do this by:\n *   (1) throwing and catching an error in the function - this will be our\n *       control error.\n *   (2) calling the component which will eventually throw an error that we'll\n *       catch - this will be our sample error.\n *   (3) diffing the control and sample error stacks to find the stack frame\n *       which represents our component.\n */\n\n\nfunction describeNativeComponentFrame(fn, construct) {\n  // If something asked for a stack inside a fake render, it should get ignored.\n  if (!fn || reentry) {\n    return '';\n  }\n\n  {\n    var frame = componentFrameCache.get(fn);\n\n    if (frame !== undefined) {\n      return frame;\n    }\n  }\n\n  reentry = true;\n  var previousPrepareStackTrace = Error.prepareStackTrace; // $FlowFixMe[incompatible-type] It does accept undefined.\n\n  Error.prepareStackTrace = undefined;\n  var previousDispatcher;\n\n  {\n    previousDispatcher = ReactCurrentDispatcher.current; // Set the dispatcher in DEV because this might be call in the render function\n    // for warnings.\n\n    ReactCurrentDispatcher.current = null;\n    disableLogs();\n  }\n  /**\n   * Finding a common stack frame between sample and control errors can be\n   * tricky given the different types and levels of stack trace truncation from\n   * different JS VMs. So instead we'll attempt to control what that common\n   * frame should be through this object method:\n   * Having both the sample and control errors be in the function under the\n   * `DescribeNativeComponentFrameRoot` property, + setting the `name` and\n   * `displayName` properties of the function ensures that a stack\n   * frame exists that has the method name `DescribeNativeComponentFrameRoot` in\n   * it for both control and sample stacks.\n   */\n\n\n  var RunInRootFrame = {\n    DetermineComponentFrameRoot: function () {\n      var control;\n\n      try {\n        // This should throw.\n        if (construct) {\n          // Something should be setting the props in the constructor.\n          var Fake = function () {\n            throw Error();\n          }; // $FlowFixMe[prop-missing]\n\n\n          Object.defineProperty(Fake.prototype, 'props', {\n            set: function () {\n              // We use a throwing setter instead of frozen or non-writable props\n              // because that won't throw in a non-strict mode function.\n              throw Error();\n            }\n          });\n\n          if (typeof Reflect === 'object' && Reflect.construct) {\n            // We construct a different control for this case to include any extra\n            // frames added by the construct call.\n            try {\n              Reflect.construct(Fake, []);\n            } catch (x) {\n              control = x;\n            }\n\n            Reflect.construct(fn, [], Fake);\n          } else {\n            try {\n              Fake.call();\n            } catch (x) {\n              control = x;\n            } // $FlowFixMe[prop-missing] found when upgrading Flow\n\n\n            fn.call(Fake.prototype);\n          }\n        } else {\n          try {\n            throw Error();\n          } catch (x) {\n            control = x;\n          } // TODO(luna): This will currently only throw if the function component\n          // tries to access React/ReactDOM/props. We should probably make this throw\n          // in simple components too\n\n\n          var maybePromise = fn(); // If the function component returns a promise, it's likely an async\n          // component, which we don't yet support. Attach a noop catch handler to\n          // silence the error.\n          // TODO: Implement component stacks for async client components?\n\n          if (maybePromise && typeof maybePromise.catch === 'function') {\n            maybePromise.catch(function () {});\n          }\n        }\n      } catch (sample) {\n        // This is inlined manually because closure doesn't do it for us.\n        if (sample && control && typeof sample.stack === 'string') {\n          return [sample.stack, control.stack];\n        }\n      }\n\n      return [null, null];\n    }\n  }; // $FlowFixMe[prop-missing]\n\n  RunInRootFrame.DetermineComponentFrameRoot.displayName = 'DetermineComponentFrameRoot';\n  var namePropDescriptor = Object.getOwnPropertyDescriptor(RunInRootFrame.DetermineComponentFrameRoot, 'name'); // Before ES6, the `name` property was not configurable.\n\n  if (namePropDescriptor && namePropDescriptor.configurable) {\n    // V8 utilizes a function's `name` property when generating a stack trace.\n    Object.defineProperty(RunInRootFrame.DetermineComponentFrameRoot, // Configurable properties can be updated even if its writable descriptor\n    // is set to `false`.\n    // $FlowFixMe[cannot-write]\n    'name', {\n      value: 'DetermineComponentFrameRoot'\n    });\n  }\n\n  try {\n    var _RunInRootFrame$Deter = RunInRootFrame.DetermineComponentFrameRoot(),\n        sampleStack = _RunInRootFrame$Deter[0],\n        controlStack = _RunInRootFrame$Deter[1];\n\n    if (sampleStack && controlStack) {\n      // This extracts the first frame from the sample that isn't also in the control.\n      // Skipping one frame that we assume is the frame that calls the two.\n      var sampleLines = sampleStack.split('\\n');\n      var controlLines = controlStack.split('\\n');\n      var s = 0;\n      var c = 0;\n\n      while (s < sampleLines.length && !sampleLines[s].includes('DetermineComponentFrameRoot')) {\n        s++;\n      }\n\n      while (c < controlLines.length && !controlLines[c].includes('DetermineComponentFrameRoot')) {\n        c++;\n      } // We couldn't find our intentionally injected common root frame, attempt\n      // to find another common root frame by search from the bottom of the\n      // control stack...\n\n\n      if (s === sampleLines.length || c === controlLines.length) {\n        s = sampleLines.length - 1;\n        c = controlLines.length - 1;\n\n        while (s >= 1 && c >= 0 && sampleLines[s] !== controlLines[c]) {\n          // We expect at least one stack frame to be shared.\n          // Typically this will be the root most one. However, stack frames may be\n          // cut off due to maximum stack limits. In this case, one maybe cut off\n          // earlier than the other. We assume that the sample is longer or the same\n          // and there for cut off earlier. So we should find the root most frame in\n          // the sample somewhere in the control.\n          c--;\n        }\n      }\n\n      for (; s >= 1 && c >= 0; s--, c--) {\n        // Next we find the first one that isn't the same which should be the\n        // frame that called our sample function and the control.\n        if (sampleLines[s] !== controlLines[c]) {\n          // In V8, the first line is describing the message but other VMs don't.\n          // If we're about to return the first line, and the control is also on the same\n          // line, that's a pretty good indicator that our sample threw at same line as\n          // the control. I.e. before we entered the sample frame. So we ignore this result.\n          // This can happen if you passed a class to function component, or non-function.\n          if (s !== 1 || c !== 1) {\n            do {\n              s--;\n              c--; // We may still have similar intermediate frames from the construct call.\n              // The next one that isn't the same should be our match though.\n\n              if (c < 0 || sampleLines[s] !== controlLines[c]) {\n                // V8 adds a \"new\" prefix for native classes. Let's remove it to make it prettier.\n                var _frame = '\\n' + sampleLines[s].replace(' at new ', ' at '); // If our component frame is labeled \"<anonymous>\"\n                // but we have a user-provided \"displayName\"\n                // splice it in to make the stack more readable.\n\n\n                if (fn.displayName && _frame.includes('<anonymous>')) {\n                  _frame = _frame.replace('<anonymous>', fn.displayName);\n                }\n\n                if (true) {\n                  if (typeof fn === 'function') {\n                    componentFrameCache.set(fn, _frame);\n                  }\n                } // Return the line we found.\n\n\n                return _frame;\n              }\n            } while (s >= 1 && c >= 0);\n          }\n\n          break;\n        }\n      }\n    }\n  } finally {\n    reentry = false;\n\n    {\n      ReactCurrentDispatcher.current = previousDispatcher;\n      reenableLogs();\n    }\n\n    Error.prepareStackTrace = previousPrepareStackTrace;\n  } // Fallback to just using the name if we couldn't make it throw.\n\n\n  var name = fn ? fn.displayName || fn.name : '';\n  var syntheticFrame = name ? describeBuiltInComponentFrame(name) : '';\n\n  {\n    if (typeof fn === 'function') {\n      componentFrameCache.set(fn, syntheticFrame);\n    }\n  }\n\n  return syntheticFrame;\n}\nfunction describeFunctionComponentFrame(fn, ownerFn) {\n  {\n    return describeNativeComponentFrame(fn, false);\n  }\n}\n\nfunction shouldConstruct(Component) {\n  var prototype = Component.prototype;\n  return !!(prototype && prototype.isReactComponent);\n}\n\nfunction describeUnknownElementTypeFrameInDEV(type, ownerFn) {\n\n  if (type == null) {\n    return '';\n  }\n\n  if (typeof type === 'function') {\n    {\n      return describeNativeComponentFrame(type, shouldConstruct(type));\n    }\n  }\n\n  if (typeof type === 'string') {\n    return describeBuiltInComponentFrame(type);\n  }\n\n  switch (type) {\n    case REACT_SUSPENSE_TYPE:\n      return describeBuiltInComponentFrame('Suspense');\n\n    case REACT_SUSPENSE_LIST_TYPE:\n      return describeBuiltInComponentFrame('SuspenseList');\n  }\n\n  if (typeof type === 'object') {\n    switch (type.$$typeof) {\n      case REACT_FORWARD_REF_TYPE:\n        return describeFunctionComponentFrame(type.render);\n\n      case REACT_MEMO_TYPE:\n        // Memo may contain any component type so we recursively resolve it.\n        return describeUnknownElementTypeFrameInDEV(type.type, ownerFn);\n\n      case REACT_LAZY_TYPE:\n        {\n          var lazyComponent = type;\n          var payload = lazyComponent._payload;\n          var init = lazyComponent._init;\n\n          try {\n            // Lazy may contain any component type so we recursively resolve it.\n            return describeUnknownElementTypeFrameInDEV(init(payload), ownerFn);\n          } catch (x) {}\n        }\n    }\n  }\n\n  return '';\n}\n\nvar ReactCurrentOwner = ReactSharedInternals.ReactCurrentOwner;\nvar ReactDebugCurrentFrame = ReactSharedInternals.ReactDebugCurrentFrame;\nvar REACT_CLIENT_REFERENCE = Symbol.for('react.client.reference');\nvar specialPropKeyWarningShown;\nvar specialPropRefWarningShown;\nvar didWarnAboutStringRefs;\n\n{\n  didWarnAboutStringRefs = {};\n}\n\nfunction hasValidRef(config) {\n  {\n    if (hasOwnProperty.call(config, 'ref')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'ref').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.ref !== undefined;\n}\n\nfunction hasValidKey(config) {\n  {\n    if (hasOwnProperty.call(config, 'key')) {\n      var getter = Object.getOwnPropertyDescriptor(config, 'key').get;\n\n      if (getter && getter.isReactWarning) {\n        return false;\n      }\n    }\n  }\n\n  return config.key !== undefined;\n}\n\nfunction warnIfStringRefCannotBeAutoConverted(config, self) {\n  {\n    if (typeof config.ref === 'string' && ReactCurrentOwner.current && self && ReactCurrentOwner.current.stateNode !== self) {\n      var componentName = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (!didWarnAboutStringRefs[componentName]) {\n        error('Component \"%s\" contains the string ref \"%s\". ' + 'Support for string refs will be removed in a future major release. ' + 'This case cannot be automatically converted to an arrow function. ' + 'We ask you to manually fix this case by using useRef() or createRef() instead. ' + 'Learn more about using refs safely here: ' + 'https://reactjs.org/link/strict-mode-string-ref', getComponentNameFromType(ReactCurrentOwner.current.type), config.ref);\n\n        didWarnAboutStringRefs[componentName] = true;\n      }\n    }\n  }\n}\n\nfunction defineKeyPropWarningGetter(props, displayName) {\n  {\n    var warnAboutAccessingKey = function () {\n      if (!specialPropKeyWarningShown) {\n        specialPropKeyWarningShown = true;\n\n        error('%s: `key` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n      }\n    };\n\n    warnAboutAccessingKey.isReactWarning = true;\n    Object.defineProperty(props, 'key', {\n      get: warnAboutAccessingKey,\n      configurable: true\n    });\n  }\n}\n\nfunction defineRefPropWarningGetter(props, displayName) {\n  {\n    {\n      var warnAboutAccessingRef = function () {\n        if (!specialPropRefWarningShown) {\n          specialPropRefWarningShown = true;\n\n          error('%s: `ref` is not a prop. Trying to access it will result ' + 'in `undefined` being returned. If you need to access the same ' + 'value within the child component, you should pass it as a different ' + 'prop. (https://reactjs.org/link/special-props)', displayName);\n        }\n      };\n\n      warnAboutAccessingRef.isReactWarning = true;\n      Object.defineProperty(props, 'ref', {\n        get: warnAboutAccessingRef,\n        configurable: true\n      });\n    }\n  }\n}\n/**\n * Factory method to create a new React element. This no longer adheres to\n * the class pattern, so do not use new to call it. Also, instanceof check\n * will not work. Instead test $$typeof field against Symbol.for('react.element') to check\n * if something is a React Element.\n *\n * @param {*} type\n * @param {*} props\n * @param {*} key\n * @param {string|object} ref\n * @param {*} owner\n * @param {*} self A *temporary* helper to detect places where `this` is\n * different from the `owner` when React.createElement is called, so that we\n * can warn. We want to get rid of owner and replace string `ref`s with arrow\n * functions, and as long as `this` and owner are the same, there will be no\n * change in behavior.\n * @param {*} source An annotation object (added by a transpiler or otherwise)\n * indicating filename, line number, and/or other information.\n * @internal\n */\n\n\nfunction ReactElement(type, key, _ref, self, source, owner, props) {\n  var ref;\n\n  {\n    ref = _ref;\n  }\n\n  var element;\n\n  {\n    // In prod, `ref` is a regular property. It will be removed in a\n    // future release.\n    element = {\n      // This tag allows us to uniquely identify this as a React Element\n      $$typeof: REACT_ELEMENT_TYPE,\n      // Built-in properties that belong on the element\n      type: type,\n      key: key,\n      ref: ref,\n      props: props,\n      // Record the component responsible for creating this element.\n      _owner: owner\n    };\n  }\n\n  {\n    // The validation flag is currently mutative. We put it on\n    // an external backing store so that we can freeze the whole object.\n    // This can be replaced with a WeakMap once they are implemented in\n    // commonly used development environments.\n    element._store = {}; // To make comparing ReactElements easier for testing purposes, we make\n    // the validation flag non-enumerable (where possible, which should\n    // include every environment we run tests in), so the test framework\n    // ignores it.\n\n    Object.defineProperty(element._store, 'validated', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: false\n    }); // debugInfo contains Server Component debug information.\n\n    Object.defineProperty(element, '_debugInfo', {\n      configurable: false,\n      enumerable: false,\n      writable: true,\n      value: null\n    });\n\n    if (Object.freeze) {\n      Object.freeze(element.props);\n      Object.freeze(element);\n    }\n  }\n\n  return element;\n}\nvar didWarnAboutKeySpread = {};\n/**\n * https://github.com/reactjs/rfcs/pull/107\n * @param {*} type\n * @param {object} props\n * @param {string} key\n */\n\nfunction jsxDEV$1(type, config, maybeKey, isStaticChildren, source, self) {\n  {\n    if (!isValidElementType(type)) {\n      // This is an invalid element type.\n      //\n      // We warn in this case but don't throw. We expect the element creation to\n      // succeed and there will likely be errors in render.\n      var info = '';\n\n      if (type === undefined || typeof type === 'object' && type !== null && Object.keys(type).length === 0) {\n        info += ' You likely forgot to export your component from the file ' + \"it's defined in, or you might have mixed up default and named imports.\";\n      }\n\n      var typeString;\n\n      if (type === null) {\n        typeString = 'null';\n      } else if (isArray(type)) {\n        typeString = 'array';\n      } else if (type !== undefined && type.$$typeof === REACT_ELEMENT_TYPE) {\n        typeString = \"<\" + (getComponentNameFromType(type.type) || 'Unknown') + \" />\";\n        info = ' Did you accidentally export a JSX literal instead of a component?';\n      } else {\n        typeString = typeof type;\n      }\n\n      error('React.jsx: type is invalid -- expected a string (for ' + 'built-in components) or a class/function (for composite ' + 'components) but got: %s.%s', typeString, info);\n    } else {\n      // This is a valid element type.\n      // Skip key warning if the type isn't valid since our key validation logic\n      // doesn't expect a non-string/function type and can throw confusing\n      // errors. We don't want exception behavior to differ between dev and\n      // prod. (Rendering will throw with a helpful message and as soon as the\n      // type is fixed, the key warnings will appear.)\n      var children = config.children;\n\n      if (children !== undefined) {\n        if (isStaticChildren) {\n          if (isArray(children)) {\n            for (var i = 0; i < children.length; i++) {\n              validateChildKeys(children[i], type);\n            }\n\n            if (Object.freeze) {\n              Object.freeze(children);\n            }\n          } else {\n            error('React.jsx: Static children should always be an array. ' + 'You are likely explicitly calling React.jsxs or React.jsxDEV. ' + 'Use the Babel transform instead.');\n          }\n        } else {\n          validateChildKeys(children, type);\n        }\n      }\n    } // Warn about key spread regardless of whether the type is valid.\n\n\n    if (hasOwnProperty.call(config, 'key')) {\n      var componentName = getComponentNameFromType(type);\n      var keys = Object.keys(config).filter(function (k) {\n        return k !== 'key';\n      });\n      var beforeExample = keys.length > 0 ? '{key: someKey, ' + keys.join(': ..., ') + ': ...}' : '{key: someKey}';\n\n      if (!didWarnAboutKeySpread[componentName + beforeExample]) {\n        var afterExample = keys.length > 0 ? '{' + keys.join(': ..., ') + ': ...}' : '{}';\n\n        error('A props object containing a \"key\" prop is being spread into JSX:\\n' + '  let props = %s;\\n' + '  <%s {...props} />\\n' + 'React keys must be passed directly to JSX without using spread:\\n' + '  let props = %s;\\n' + '  <%s key={someKey} {...props} />', beforeExample, componentName, afterExample, componentName);\n\n        didWarnAboutKeySpread[componentName + beforeExample] = true;\n      }\n    }\n\n    var propName; // Reserved names are extracted\n\n    var props = {};\n    var key = null;\n    var ref = null; // Currently, key can be spread in as a prop. This causes a potential\n    // issue if key is also explicitly declared (ie. <div {...props} key=\"Hi\" />\n    // or <div key=\"Hi\" {...props} /> ). We want to deprecate key spread,\n    // but as an intermediary step, we will use jsxDEV for everything except\n    // <div {...props} key=\"Hi\" />, because we aren't currently able to tell if\n    // key is explicitly declared to be undefined or not.\n\n    if (maybeKey !== undefined) {\n      {\n        checkKeyStringCoercion(maybeKey);\n      }\n\n      key = '' + maybeKey;\n    }\n\n    if (hasValidKey(config)) {\n      {\n        checkKeyStringCoercion(config.key);\n      }\n\n      key = '' + config.key;\n    }\n\n    if (hasValidRef(config)) {\n      {\n        ref = config.ref;\n      }\n\n      warnIfStringRefCannotBeAutoConverted(config, self);\n    } // Remaining properties are added to a new props object\n\n\n    for (propName in config) {\n      if (hasOwnProperty.call(config, propName) && // Skip over reserved prop names\n      propName !== 'key' && (propName !== 'ref')) {\n        props[propName] = config[propName];\n      }\n    } // Resolve default props\n\n\n    if (type && type.defaultProps) {\n      var defaultProps = type.defaultProps;\n\n      for (propName in defaultProps) {\n        if (props[propName] === undefined) {\n          props[propName] = defaultProps[propName];\n        }\n      }\n    }\n\n    if (key || ref) {\n      var displayName = typeof type === 'function' ? type.displayName || type.name || 'Unknown' : type;\n\n      if (key) {\n        defineKeyPropWarningGetter(props, displayName);\n      }\n\n      if (ref) {\n        defineRefPropWarningGetter(props, displayName);\n      }\n    }\n\n    var element = ReactElement(type, key, ref, self, source, ReactCurrentOwner.current, props);\n\n    if (type === REACT_FRAGMENT_TYPE) {\n      validateFragmentProps(element);\n    }\n\n    return element;\n  }\n}\n\nfunction getDeclarationErrorAddendum() {\n  {\n    if (ReactCurrentOwner.current) {\n      var name = getComponentNameFromType(ReactCurrentOwner.current.type);\n\n      if (name) {\n        return '\\n\\nCheck the render method of `' + name + '`.';\n      }\n    }\n\n    return '';\n  }\n}\n/**\n * Ensure that every element either is passed in a static location, in an\n * array with an explicit keys property defined, or in an object literal\n * with valid key property.\n *\n * @internal\n * @param {ReactNode} node Statically passed child of any type.\n * @param {*} parentType node's parent's type.\n */\n\n\nfunction validateChildKeys(node, parentType) {\n  {\n    if (typeof node !== 'object' || !node) {\n      return;\n    }\n\n    if (node.$$typeof === REACT_CLIENT_REFERENCE) ; else if (isArray(node)) {\n      for (var i = 0; i < node.length; i++) {\n        var child = node[i];\n\n        if (isValidElement(child)) {\n          validateExplicitKey(child, parentType);\n        }\n      }\n    } else if (isValidElement(node)) {\n      // This element was passed in a valid location.\n      if (node._store) {\n        node._store.validated = true;\n      }\n    } else {\n      var iteratorFn = getIteratorFn(node);\n\n      if (typeof iteratorFn === 'function') {\n        // Entry iterators used to provide implicit keys,\n        // but now we print a separate warning for them later.\n        if (iteratorFn !== node.entries) {\n          var iterator = iteratorFn.call(node);\n          var step;\n\n          while (!(step = iterator.next()).done) {\n            if (isValidElement(step.value)) {\n              validateExplicitKey(step.value, parentType);\n            }\n          }\n        }\n      }\n    }\n  }\n}\n/**\n * Verifies the object is a ReactElement.\n * See https://reactjs.org/docs/react-api.html#isvalidelement\n * @param {?object} object\n * @return {boolean} True if `object` is a ReactElement.\n * @final\n */\n\n\nfunction isValidElement(object) {\n  return typeof object === 'object' && object !== null && object.$$typeof === REACT_ELEMENT_TYPE;\n}\nvar ownerHasKeyUseWarning = {};\n/**\n * Warn if the element doesn't have an explicit key assigned to it.\n * This element is in an array. The array could grow and shrink or be\n * reordered. All children that haven't already been validated are required to\n * have a \"key\" property assigned to it. Error statuses are cached so a warning\n * will only be shown once.\n *\n * @internal\n * @param {ReactElement} element Element that requires a key.\n * @param {*} parentType element's parent's type.\n */\n\nfunction validateExplicitKey(element, parentType) {\n  {\n    if (!element._store || element._store.validated || element.key != null) {\n      return;\n    }\n\n    element._store.validated = true;\n    var currentComponentErrorInfo = getCurrentComponentErrorInfo(parentType);\n\n    if (ownerHasKeyUseWarning[currentComponentErrorInfo]) {\n      return;\n    }\n\n    ownerHasKeyUseWarning[currentComponentErrorInfo] = true; // Usually the current owner is the offender, but if it accepts children as a\n    // property, it may be the creator of the child that's responsible for\n    // assigning it a key.\n\n    var childOwner = '';\n\n    if (element && element._owner && element._owner !== ReactCurrentOwner.current) {\n      // Give the component that originally created this child.\n      childOwner = \" It was passed a child from \" + getComponentNameFromType(element._owner.type) + \".\";\n    }\n\n    setCurrentlyValidatingElement(element);\n\n    error('Each child in a list should have a unique \"key\" prop.' + '%s%s See https://reactjs.org/link/warning-keys for more information.', currentComponentErrorInfo, childOwner);\n\n    setCurrentlyValidatingElement(null);\n  }\n}\n\nfunction setCurrentlyValidatingElement(element) {\n  {\n    if (element) {\n      var owner = element._owner;\n      var stack = describeUnknownElementTypeFrameInDEV(element.type, owner ? owner.type : null);\n      ReactDebugCurrentFrame.setExtraStackFrame(stack);\n    } else {\n      ReactDebugCurrentFrame.setExtraStackFrame(null);\n    }\n  }\n}\n\nfunction getCurrentComponentErrorInfo(parentType) {\n  {\n    var info = getDeclarationErrorAddendum();\n\n    if (!info) {\n      var parentName = getComponentNameFromType(parentType);\n\n      if (parentName) {\n        info = \"\\n\\nCheck the top-level render call using <\" + parentName + \">.\";\n      }\n    }\n\n    return info;\n  }\n}\n/**\n * Given a fragment, validate that it can only be provided with fragment props\n * @param {ReactElement} fragment\n */\n\n\nfunction validateFragmentProps(fragment) {\n  // TODO: Move this to render phase instead of at element creation.\n  {\n    var keys = Object.keys(fragment.props);\n\n    for (var i = 0; i < keys.length; i++) {\n      var key = keys[i];\n\n      if (key !== 'children' && key !== 'key') {\n        setCurrentlyValidatingElement(fragment);\n\n        error('Invalid prop `%s` supplied to `React.Fragment`. ' + 'React.Fragment can only have `key` and `children` props.', key);\n\n        setCurrentlyValidatingElement(null);\n        break;\n      }\n    }\n\n    if (fragment.ref !== null) {\n      setCurrentlyValidatingElement(fragment);\n\n      error('Invalid attribute `ref` supplied to `React.Fragment`.');\n\n      setCurrentlyValidatingElement(null);\n    }\n  }\n}\n\nvar jsxDEV = jsxDEV$1 ;\n\nexports.Fragment = REACT_FRAGMENT_TYPE;\nexports.jsxDEV = jsxDEV;\n  })();\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js":
/*!******************************************************************!*\
  !*** ./node_modules/next/dist/compiled/react/jsx-dev-runtime.js ***!
  \******************************************************************/
/***/ (function(module, __unused_webpack_exports, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("\n\nif (false) {} else {\n  module.exports = __webpack_require__(/*! ./cjs/react-jsx-dev-runtime.development.js */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/cjs/react-jsx-dev-runtime.development.js\");\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKGFwcC1wYWdlcy1icm93c2VyKS8uL25vZGVfbW9kdWxlcy9uZXh0L2Rpc3QvY29tcGlsZWQvcmVhY3QvanN4LWRldi1ydW50aW1lLmpzIiwibWFwcGluZ3MiOiJBQUFhOztBQUViLElBQUksS0FBcUMsRUFBRSxFQUUxQyxDQUFDO0FBQ0YsRUFBRSw4TEFBc0U7QUFDeEUiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9fTl9FLy4vbm9kZV9tb2R1bGVzL25leHQvZGlzdC9jb21waWxlZC9yZWFjdC9qc3gtZGV2LXJ1bnRpbWUuanM/ZDY0NSJdLCJzb3VyY2VzQ29udGVudCI6WyIndXNlIHN0cmljdCc7XG5cbmlmIChwcm9jZXNzLmVudi5OT0RFX0VOViA9PT0gJ3Byb2R1Y3Rpb24nKSB7XG4gIG1vZHVsZS5leHBvcnRzID0gcmVxdWlyZSgnLi9janMvcmVhY3QtanN4LWRldi1ydW50aW1lLnByb2R1Y3Rpb24ubWluLmpzJyk7XG59IGVsc2Uge1xuICBtb2R1bGUuZXhwb3J0cyA9IHJlcXVpcmUoJy4vY2pzL3JlYWN0LWpzeC1kZXYtcnVudGltZS5kZXZlbG9wbWVudC5qcycpO1xufVxuIl0sIm5hbWVzIjpbXSwic291cmNlUm9vdCI6IiJ9\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next/dist/compiled/react/jsx-dev-runtime.js\n"));

/***/ }),

/***/ "(app-pages-browser)/./node_modules/next-themes/dist/index.mjs":
/*!*************************************************!*\
  !*** ./node_modules/next-themes/dist/index.mjs ***!
  \*************************************************/
/***/ (function(__unused_webpack___webpack_module__, __webpack_exports__, __webpack_require__) {

"use strict";
eval(__webpack_require__.ts("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: function() { return /* binding */ J; },\n/* harmony export */   useTheme: function() { return /* binding */ z; }\n/* harmony export */ });\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react */ \"(app-pages-browser)/./node_modules/next/dist/compiled/react/index.js\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider,useTheme auto */ var _s = $RefreshSig$(), _s1 = $RefreshSig$(), _s2 = $RefreshSig$();\n\nvar M = (e, i, s, u, m, a, l, h)=>{\n    let d = document.documentElement, w = [\n        \"light\",\n        \"dark\"\n    ];\n    function p(n) {\n        (Array.isArray(e) ? e : [\n            e\n        ]).forEach((y)=>{\n            let k = y === \"class\", S = k && a ? m.map((f)=>a[f] || f) : m;\n            k ? (d.classList.remove(...S), d.classList.add(a && a[n] ? a[n] : n)) : d.setAttribute(y, n);\n        }), R(n);\n    }\n    function R(n) {\n        h && w.includes(n) && (d.style.colorScheme = n);\n    }\n    function c() {\n        return window.matchMedia(\"(prefers-color-scheme: dark)\").matches ? \"dark\" : \"light\";\n    }\n    if (u) p(u);\n    else try {\n        let n = localStorage.getItem(i) || s, y = l && n === \"system\" ? c() : n;\n        p(y);\n    } catch (n) {}\n};\n_c = M;\nvar b = [\n    \"light\",\n    \"dark\"\n], I = \"(prefers-color-scheme: dark)\", O = typeof window == \"undefined\", x = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createContext(void 0), U = {\n    setTheme: (e)=>{},\n    themes: []\n}, z = ()=>{\n    _s();\n    var e;\n    return (e = react__WEBPACK_IMPORTED_MODULE_0__.useContext(x)) != null ? e : U;\n}, J = (e)=>{\n    _s1();\n    return react__WEBPACK_IMPORTED_MODULE_0__.useContext(x) ? /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(react__WEBPACK_IMPORTED_MODULE_0__.Fragment, null, e.children) : /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(V, {\n        ...e\n    });\n}, N = [\n    \"light\",\n    \"dark\"\n], V = (param)=>{\n    let { forcedTheme: e, disableTransitionOnChange: i = !1, enableSystem: s = !0, enableColorScheme: u = !0, storageKey: m = \"theme\", themes: a = N, defaultTheme: l = s ? \"system\" : \"light\", attribute: h = \"data-theme\", value: d, children: w, nonce: p, scriptProps: R } = param;\n    _s2();\n    let [c, n] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>H(m, l)), [T, y] = react__WEBPACK_IMPORTED_MODULE_0__.useState(()=>c === \"system\" ? E() : c), k = d ? Object.values(d) : a, S = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((o)=>{\n        let r = o;\n        if (!r) return;\n        o === \"system\" && s && (r = E());\n        let v = d ? d[r] : r, C = i ? W(p) : null, P = document.documentElement, L = (g)=>{\n            g === \"class\" ? (P.classList.remove(...k), v && P.classList.add(v)) : g.startsWith(\"data-\") && (v ? P.setAttribute(g, v) : P.removeAttribute(g));\n        };\n        if (Array.isArray(h) ? h.forEach(L) : L(h), u) {\n            let g = b.includes(l) ? l : null, D = b.includes(r) ? r : g;\n            P.style.colorScheme = D;\n        }\n        C == null || C();\n    }, [\n        p\n    ]), f = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((o)=>{\n        let r = typeof o == \"function\" ? o(c) : o;\n        n(r);\n        try {\n            localStorage.setItem(m, r);\n        } catch (v) {}\n    }, [\n        c\n    ]), A = react__WEBPACK_IMPORTED_MODULE_0__.useCallback((o)=>{\n        let r = E(o);\n        y(r), c === \"system\" && s && !e && S(\"system\");\n    }, [\n        c,\n        e\n    ]);\n    react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let o = window.matchMedia(I);\n        return o.addListener(A), A(o), ()=>o.removeListener(A);\n    }, [\n        A\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        let o = (r)=>{\n            r.key === m && (r.newValue ? n(r.newValue) : f(l));\n        };\n        return window.addEventListener(\"storage\", o), ()=>window.removeEventListener(\"storage\", o);\n    }, [\n        f\n    ]), react__WEBPACK_IMPORTED_MODULE_0__.useEffect(()=>{\n        S(e != null ? e : c);\n    }, [\n        e,\n        c\n    ]);\n    let Q = react__WEBPACK_IMPORTED_MODULE_0__.useMemo(()=>({\n            theme: c,\n            setTheme: f,\n            forcedTheme: e,\n            resolvedTheme: c === \"system\" ? T : c,\n            themes: s ? [\n                ...a,\n                \"system\"\n            ] : a,\n            systemTheme: s ? T : void 0\n        }), [\n        c,\n        f,\n        e,\n        T,\n        s,\n        a\n    ]);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(x.Provider, {\n        value: Q\n    }, /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(_, {\n        forcedTheme: e,\n        storageKey: m,\n        attribute: h,\n        enableSystem: s,\n        enableColorScheme: u,\n        defaultTheme: l,\n        value: d,\n        themes: a,\n        nonce: p,\n        scriptProps: R\n    }), w);\n}, _ = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.memo((param)=>{\n    let { forcedTheme: e, storageKey: i, attribute: s, enableSystem: u, enableColorScheme: m, defaultTheme: a, value: l, themes: h, nonce: d, scriptProps: w } = param;\n    let p = JSON.stringify([\n        s,\n        i,\n        a,\n        e,\n        h,\n        l,\n        u,\n        m\n    ]).slice(1, -1);\n    return /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_0__.createElement(\"script\", {\n        ...w,\n        suppressHydrationWarning: !0,\n        nonce: typeof window == \"undefined\" ? d : \"\",\n        dangerouslySetInnerHTML: {\n            __html: \"(\".concat(M.toString(), \")(\").concat(p, \")\")\n        }\n    });\n}), H = (e, i)=>{\n    if (O) return;\n    let s;\n    try {\n        s = localStorage.getItem(e) || void 0;\n    } catch (u) {}\n    return s || i;\n}, W = (e)=>{\n    let i = document.createElement(\"style\");\n    return e && i.setAttribute(\"nonce\", e), i.appendChild(document.createTextNode(\"*,*::before,*::after{-webkit-transition:none!important;-moz-transition:none!important;-o-transition:none!important;-ms-transition:none!important;transition:none!important}\")), document.head.appendChild(i), ()=>{\n        window.getComputedStyle(document.body), setTimeout(()=>{\n            document.head.removeChild(i);\n        }, 1);\n    };\n}, E = (e)=>(e || (e = window.matchMedia(I)), e.matches ? \"dark\" : \"light\");\n_s(z, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n_s1(J, \"gDsCjeeItUuvgOWf1v4qoK9RF6k=\");\n_s2(V, \"ECnLW8fNsuPePKa5HKHCpT5yuVU=\");\n\nvar _c;\n$RefreshReg$(_c, \"M\");\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(app-pages-browser)/./node_modules/next-themes/dist/index.mjs\n"));

/***/ })

},
/******/ function(__webpack_require__) { // webpackRuntimeModules
/******/ var __webpack_exec__ = function(moduleId) { return __webpack_require__(__webpack_require__.s = moduleId); }
/******/ __webpack_require__.O(0, ["main-app"], function() { return __webpack_exec__("(app-pages-browser)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=false!"); });
/******/ var __webpack_exports__ = __webpack_require__.O();
/******/ _N_E = __webpack_exports__;
/******/ }
]);