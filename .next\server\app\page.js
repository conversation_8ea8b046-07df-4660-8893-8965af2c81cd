/*
 * ATTENTION: An "eval-source-map" devtool has been used.
 * This devtool is neither made for production nor for readable output files.
 * It uses "eval()" calls to create a separate source file with attached SourceMaps in the browser devtools.
 * If you are trying to read the output file, select a different devtool (https://webpack.js.org/configuration/devtool/)
 * or disable the default devtool with "devtool: false".
 * If you are looking for production-ready output files, see mode: "production" (https://webpack.js.org/configuration/mode/).
 */
(() => {
var exports = {};
exports.id = "app/page";
exports.ids = ["app/page"];
exports.modules = {

/***/ "../../client/components/action-async-storage.external":
/*!*******************************************************************************!*\
  !*** external "next/dist/client/components/action-async-storage.external.js" ***!
  \*******************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/action-async-storage.external.js");

/***/ }),

/***/ "../../client/components/request-async-storage.external":
/*!********************************************************************************!*\
  !*** external "next/dist/client/components/request-async-storage.external.js" ***!
  \********************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/request-async-storage.external.js");

/***/ }),

/***/ "../../client/components/static-generation-async-storage.external":
/*!******************************************************************************************!*\
  !*** external "next/dist/client/components/static-generation-async-storage.external.js" ***!
  \******************************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/client/components/static-generation-async-storage.external.js");

/***/ }),

/***/ "next/dist/compiled/next-server/app-page.runtime.dev.js":
/*!*************************************************************************!*\
  !*** external "next/dist/compiled/next-server/app-page.runtime.dev.js" ***!
  \*************************************************************************/
/***/ ((module) => {

"use strict";
module.exports = require("next/dist/compiled/next-server/app-page.runtime.dev.js");

/***/ }),

/***/ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!":
/*!********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D! ***!
  \********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   GlobalError: () => (/* reexport default from dynamic */ next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default.a),\n/* harmony export */   __next_app__: () => (/* binding */ __next_app__),\n/* harmony export */   originalPathname: () => (/* binding */ originalPathname),\n/* harmony export */   pages: () => (/* binding */ pages),\n/* harmony export */   routeModule: () => (/* binding */ routeModule),\n/* harmony export */   tree: () => (/* binding */ tree)\n/* harmony export */ });\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/server/future/route-modules/app-page/module.compiled */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/module.compiled.js?d969\");\n/* harmony import */ var next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/dist/server/future/route-kind */ \"(rsc)/./node_modules/next/dist/server/future/route-kind.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next/dist/client/components/error-boundary */ \"(rsc)/./node_modules/next/dist/client/components/error-boundary.js\");\n/* harmony import */ var next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2___default = /*#__PURE__*/__webpack_require__.n(next_dist_client_components_error_boundary__WEBPACK_IMPORTED_MODULE_2__);\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! next/dist/server/app-render/entry-base */ \"(rsc)/./node_modules/next/dist/server/app-render/entry-base.js\");\n/* harmony import */ var next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3___default = /*#__PURE__*/__webpack_require__.n(next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__);\n/* harmony reexport (unknown) */ var __WEBPACK_REEXPORT_OBJECT__ = {};\n/* harmony reexport (unknown) */ for(const __WEBPACK_IMPORT_KEY__ in next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__) if([\"default\",\"tree\",\"pages\",\"GlobalError\",\"originalPathname\",\"__next_app__\",\"routeModule\"].indexOf(__WEBPACK_IMPORT_KEY__) < 0) __WEBPACK_REEXPORT_OBJECT__[__WEBPACK_IMPORT_KEY__] = () => next_dist_server_app_render_entry_base__WEBPACK_IMPORTED_MODULE_3__[__WEBPACK_IMPORT_KEY__]\n/* harmony reexport (unknown) */ __webpack_require__.d(__webpack_exports__, __WEBPACK_REEXPORT_OBJECT__);\n\"TURBOPACK { transition: next-ssr }\";\n\n\n// We inject the tree and pages here so that we can use them in the route\n// module.\nconst tree = {\n        children: [\n        '',\n        {\n        children: ['__PAGE__', {}, {\n          page: [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/page.tsx */ \"(rsc)/./app/page.tsx\")), \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\"],\n          \n        }]\n      },\n        {\n        'layout': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./app/layout.tsx */ \"(rsc)/./app/layout.tsx\")), \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\layout.tsx\"],\n'not-found': [() => Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! next/dist/client/components/not-found-error */ \"(rsc)/./node_modules/next/dist/client/components/not-found-error.js\", 23)), \"next/dist/client/components/not-found-error\"],\n        \n      }\n      ]\n      }.children;\nconst pages = [\"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\"];\n\n\nconst __next_app_require__ = __webpack_require__\nconst __next_app_load_chunk__ = () => Promise.resolve()\nconst originalPathname = \"/page\";\nconst __next_app__ = {\n    require: __next_app_require__,\n    loadChunk: __next_app_load_chunk__\n};\n\n// Create and export the route module that will be consumed.\nconst routeModule = new next_dist_server_future_route_modules_app_page_module_compiled__WEBPACK_IMPORTED_MODULE_0__.AppPageRouteModule({\n    definition: {\n        kind: next_dist_server_future_route_kind__WEBPACK_IMPORTED_MODULE_1__.RouteKind.APP_PAGE,\n        page: \"/page\",\n        pathname: \"/\",\n        // The following aren't used in production.\n        bundlePath: \"\",\n        filename: \"\",\n        appPaths: []\n    },\n    userland: {\n        loaderTree: tree\n    }\n});\n\n//# sourceMappingURL=app-page.js.map//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./components/theme-provider.tsx */ \"(ssr)/./components/theme-provider.tsx\"));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.bind(__webpack_require__, /*! ./lib/auth-context.tsx */ \"(ssr)/./lib/auth-context.tsx\"));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Ccomponents%5C%5Ctheme-provider.tsx%22%2C%22ids%22%3A%5B%22ThemeProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Clib%5C%5Cauth-context.tsx%22%2C%22ids%22%3A%5B%22AuthProvider%22%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22Inter%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-inter%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22inter%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cfont%5C%5Cgoogle%5C%5Ctarget.css%3F%7B%5C%22path%5C%22%3A%5C%22app%5C%5C%5C%5Clayout.tsx%5C%22%2C%5C%22import%5C%22%3A%5C%22JetBrains_Mono%5C%22%2C%5C%22arguments%5C%22%3A%5B%7B%5C%22subsets%5C%22%3A%5B%5C%22latin%5C%22%5D%2C%5C%22display%5C%22%3A%5C%22swap%5C%22%2C%5C%22variable%5C%22%3A%5C%22--font-jetbrains-mono%5C%22%7D%5D%2C%5C%22variableName%5C%22%3A%5C%22jetbrainsMono%5C%22%7D%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Capp%5C%5Cglobals.css%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!":
/*!**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true! ***!
  \**********************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/app-router.js */ \"(ssr)/./node_modules/next/dist/client/components/app-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/client-page.js */ \"(ssr)/./node_modules/next/dist/client/components/client-page.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/error-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/error-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/layout-router.js */ \"(ssr)/./node_modules/next/dist/client/components/layout-router.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/not-found-boundary.js */ \"(ssr)/./node_modules/next/dist/client/components/not-found-boundary.js\", 23));\n;\nPromise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/components/render-from-template-context.js */ \"(ssr)/./node_modules/next/dist/client/components/render-from-template-context.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Capp-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cclient-page.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cerror-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Clayout-router.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Cnot-found-boundary.js%22%2C%22ids%22%3A%5B%5D%7D&modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Ccomponents%5C%5Crender-from-template-context.js%22%2C%22ids%22%3A%5B%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!":
/*!************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************!*\
  !*** ./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true! ***!
  \************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************************/
/***/ ((__unused_webpack_module, __unused_webpack_exports, __webpack_require__) => {

eval("Promise.resolve(/*! import() eager */).then(__webpack_require__.t.bind(__webpack_require__, /*! ./node_modules/next/dist/client/link.js */ \"(ssr)/./node_modules/next/dist/client/link.js\", 23));\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9ub2RlX21vZHVsZXMvbmV4dC9kaXN0L2J1aWxkL3dlYnBhY2svbG9hZGVycy9uZXh0LWZsaWdodC1jbGllbnQtZW50cnktbG9hZGVyLmpzP21vZHVsZXM9JTdCJTIycmVxdWVzdCUyMiUzQSUyMkUlM0ElNUMlNUNUZW1wbyU1QyU1Q01pY3JvJTIwU2FhcyUyMFJlc2VhcmNoJTVDJTVDUmVzZWFyY2hlcyUyMG9uJTIwTWljcm8lMjBTQUFTJTIwT3Bwb3J0dW5pdGllcyU1QyU1Q0RlYWxWZXJzZSUyME9TJTVDJTVDRGVhbFZlcnNlJTIwT1MlMjBBcHAlNUMlNUNub2RlX21vZHVsZXMlNUMlNUNuZXh0JTVDJTVDZGlzdCU1QyU1Q2NsaWVudCU1QyU1Q2xpbmsuanMlMjIlMkMlMjJpZHMlMjIlM0ElNUIlMjIqJTIyJTVEJTdEJnNlcnZlcj10cnVlISIsIm1hcHBpbmdzIjoiQUFBQSxnTUFBc0wiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWFsdmVyc2Utb3MvPzhhMzMiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0KC8qIHdlYnBhY2tNb2RlOiBcImVhZ2VyXCIgKi8gXCJFOlxcXFxUZW1wb1xcXFxNaWNybyBTYWFzIFJlc2VhcmNoXFxcXFJlc2VhcmNoZXMgb24gTWljcm8gU0FBUyBPcHBvcnR1bml0aWVzXFxcXERlYWxWZXJzZSBPU1xcXFxEZWFsVmVyc2UgT1MgQXBwXFxcXG5vZGVfbW9kdWxlc1xcXFxuZXh0XFxcXGRpc3RcXFxcY2xpZW50XFxcXGxpbmsuanNcIik7XG4iXSwibmFtZXMiOltdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./node_modules/next/dist/build/webpack/loaders/next-flight-client-entry-loader.js?modules=%7B%22request%22%3A%22E%3A%5C%5CTempo%5C%5CMicro%20Saas%20Research%5C%5CResearches%20on%20Micro%20SAAS%20Opportunities%5C%5CDealVerse%20OS%5C%5CDealVerse%20OS%20App%5C%5Cnode_modules%5C%5Cnext%5C%5Cdist%5C%5Cclient%5C%5Clink.js%22%2C%22ids%22%3A%5B%22*%22%5D%7D&server=true!\n");

/***/ }),

/***/ "(ssr)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   ThemeProvider: () => (/* binding */ ThemeProvider)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var next_themes__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! next-themes */ \"(ssr)/./node_modules/next-themes/dist/index.mjs\");\n/* __next_internal_client_entry_do_not_use__ ThemeProvider auto */ \n\n\nfunction ThemeProvider({ children, defaultTheme = \"light\", enableSystem = true, disableTransitionOnChange = false }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_themes__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n        attribute: \"class\",\n        defaultTheme: defaultTheme,\n        enableSystem: enableSystem,\n        disableTransitionOnChange: disableTransitionOnChange,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\theme-provider.tsx\",\n        lineNumber: 18,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHNzcikvLi9jb21wb25lbnRzL3RoZW1lLXByb3ZpZGVyLnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7O0FBRThCO0FBQ21DO0FBRTFELFNBQVNDLGNBQWMsRUFDNUJFLFFBQVEsRUFDUkMsZUFBZSxPQUFPLEVBQ3RCQyxlQUFlLElBQUksRUFDbkJDLDRCQUE0QixLQUFLLEVBTWxDO0lBQ0MscUJBQ0UsOERBQUNKLHNEQUFrQkE7UUFDakJLLFdBQVU7UUFDVkgsY0FBY0E7UUFDZEMsY0FBY0E7UUFDZEMsMkJBQTJCQTtrQkFFMUJIOzs7Ozs7QUFHUCIsInNvdXJjZXMiOlsid2VicGFjazovL2RlYWx2ZXJzZS1vcy8uL2NvbXBvbmVudHMvdGhlbWUtcHJvdmlkZXIudHN4PzkyODkiXSwic291cmNlc0NvbnRlbnQiOlsiXCJ1c2UgY2xpZW50XCJcblxuaW1wb3J0ICogYXMgUmVhY3QgZnJvbSBcInJlYWN0XCJcbmltcG9ydCB7IFRoZW1lUHJvdmlkZXIgYXMgTmV4dFRoZW1lc1Byb3ZpZGVyIH0gZnJvbSBcIm5leHQtdGhlbWVzXCJcblxuZXhwb3J0IGZ1bmN0aW9uIFRoZW1lUHJvdmlkZXIoe1xuICBjaGlsZHJlbixcbiAgZGVmYXVsdFRoZW1lID0gXCJsaWdodFwiLFxuICBlbmFibGVTeXN0ZW0gPSB0cnVlLFxuICBkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlID0gZmFsc2Vcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxuICBkZWZhdWx0VGhlbWU/OiBzdHJpbmdcbiAgZW5hYmxlU3lzdGVtPzogYm9vbGVhblxuICBkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlPzogYm9vbGVhblxufSkge1xuICByZXR1cm4gKFxuICAgIDxOZXh0VGhlbWVzUHJvdmlkZXJcbiAgICAgIGF0dHJpYnV0ZT1cImNsYXNzXCJcbiAgICAgIGRlZmF1bHRUaGVtZT17ZGVmYXVsdFRoZW1lfVxuICAgICAgZW5hYmxlU3lzdGVtPXtlbmFibGVTeXN0ZW19XG4gICAgICBkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlPXtkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlfVxuICAgID5cbiAgICAgIHtjaGlsZHJlbn1cbiAgICA8L05leHRUaGVtZXNQcm92aWRlcj5cbiAgKVxufVxuIl0sIm5hbWVzIjpbIlJlYWN0IiwiVGhlbWVQcm92aWRlciIsIk5leHRUaGVtZXNQcm92aWRlciIsImNoaWxkcmVuIiwiZGVmYXVsdFRoZW1lIiwiZW5hYmxlU3lzdGVtIiwiZGlzYWJsZVRyYW5zaXRpb25PbkNoYW5nZSIsImF0dHJpYnV0ZSJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(ssr)/./components/theme-provider.tsx\n");

/***/ }),

/***/ "(ssr)/./lib/api-client.ts":
/*!***************************!*\
  !*** ./lib/api-client.ts ***!
  \***************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   apiClient: () => (/* binding */ apiClient)\n/* harmony export */ });\n/**\n * API Client for DealVerse OS Backend\n */ const API_BASE_URL = \"http://localhost:8000/api/v1\" || 0;\nclass ApiClient {\n    constructor(baseUrl = API_BASE_URL){\n        this.token = null;\n        this.baseUrl = baseUrl;\n        // Get token from localStorage if available\n        if (false) {}\n    }\n    setToken(token) {\n        this.token = token;\n        if (false) {}\n    }\n    clearToken() {\n        this.token = null;\n        if (false) {}\n    }\n    async request(endpoint, options = {}) {\n        const url = `${this.baseUrl}${endpoint}`;\n        const headers = {\n            \"Content-Type\": \"application/json\",\n            ...options.headers || {}\n        };\n        if (this.token) {\n            headers.Authorization = `Bearer ${this.token}`;\n        }\n        try {\n            const response = await fetch(url, {\n                ...options,\n                headers\n            });\n            if (!response.ok) {\n                if (response.status === 401) {\n                    // Token expired, try to refresh\n                    const refreshed = await this.refreshToken();\n                    if (refreshed) {\n                        // Retry the request with new token\n                        headers.Authorization = `Bearer ${this.token}`;\n                        const retryResponse = await fetch(url, {\n                            ...options,\n                            headers\n                        });\n                        if (retryResponse.ok) {\n                            const data = await retryResponse.json();\n                            return {\n                                data\n                            };\n                        }\n                    }\n                    // Refresh failed, clear tokens and redirect to login\n                    this.clearToken();\n                    if (false) {}\n                    throw new Error(\"Authentication failed\");\n                }\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(errorData.detail || errorData.message || `HTTP ${response.status}`);\n            }\n            const data = await response.json();\n            return {\n                data\n            };\n        } catch (error) {\n            console.error(\"API request failed:\", error);\n            // Check if it's a network error\n            if (error instanceof TypeError && error.message.includes(\"fetch\")) {\n                return {\n                    error: \"Unable to connect to server. Please check your connection and try again.\"\n                };\n            }\n            return {\n                error: error instanceof Error ? error.message : \"Unknown error\"\n            };\n        }\n    }\n    async refreshToken() {\n        const refreshToken =  false ? 0 : null;\n        if (!refreshToken) return false;\n        try {\n            const response = await fetch(`${this.baseUrl}/auth/refresh`, {\n                method: \"POST\",\n                headers: {\n                    \"Content-Type\": \"application/json\"\n                },\n                body: JSON.stringify({\n                    refresh_token: refreshToken\n                })\n            });\n            if (response.ok) {\n                const data = await response.json();\n                this.setToken(data.access_token);\n                if (false) {}\n                return true;\n            }\n        } catch (error) {\n            console.error(\"Token refresh failed:\", error);\n        }\n        return false;\n    }\n    // Authentication endpoints\n    async login(email, password) {\n        return this.request(\"/auth/login/json\", {\n            method: \"POST\",\n            body: JSON.stringify({\n                email,\n                password\n            })\n        });\n    }\n    async register(userData) {\n        return this.request(\"/auth/register\", {\n            method: \"POST\",\n            body: JSON.stringify(userData)\n        });\n    }\n    async logout() {\n        const result = await this.request(\"/auth/logout\", {\n            method: \"POST\"\n        });\n        this.clearToken();\n        return result;\n    }\n    // User endpoints\n    async getCurrentUser() {\n        return this.request(\"/users/me\");\n    }\n    async updateCurrentUser(userData) {\n        return this.request(\"/users/me\", {\n            method: \"PUT\",\n            body: JSON.stringify(userData)\n        });\n    }\n    async getUsers(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/users?${query.toString()}`);\n    }\n    // Deal endpoints\n    async getDeals(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.stage) query.append(\"stage\", params.stage);\n        if (params?.status) query.append(\"status\", params.status);\n        return this.request(`/deals?${query.toString()}`);\n    }\n    async getDeal(id) {\n        return this.request(`/deals/${id}`);\n    }\n    async createDeal(dealData) {\n        return this.request(\"/deals\", {\n            method: \"POST\",\n            body: JSON.stringify(dealData)\n        });\n    }\n    async updateDeal(id, dealData) {\n        return this.request(`/deals/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(dealData)\n        });\n    }\n    async deleteDeal(id) {\n        return this.request(`/deals/${id}`, {\n            method: \"DELETE\"\n        });\n    }\n    async getDealsStats() {\n        return this.request(\"/deals/stats/summary\");\n    }\n    // Client endpoints\n    async getClients(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.client_type) query.append(\"client_type\", params.client_type);\n        if (params?.industry) query.append(\"industry\", params.industry);\n        if (params?.search) query.append(\"search\", params.search);\n        return this.request(`/clients?${query.toString()}`);\n    }\n    async getClient(id) {\n        return this.request(`/clients/${id}`);\n    }\n    async createClient(clientData) {\n        return this.request(\"/clients\", {\n            method: \"POST\",\n            body: JSON.stringify(clientData)\n        });\n    }\n    async updateClient(id, clientData) {\n        return this.request(`/clients/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(clientData)\n        });\n    }\n    async deleteClient(id) {\n        return this.request(`/clients/${id}`, {\n            method: \"DELETE\"\n        });\n    }\n    // Analytics endpoints\n    async getDashboardAnalytics() {\n        return this.request(\"/analytics/dashboard\");\n    }\n    async getDealsPerformance(days = 30) {\n        return this.request(`/analytics/deals/performance?days=${days}`);\n    }\n    async getClientInsights() {\n        return this.request(\"/analytics/clients/insights\");\n    }\n    async getTeamProductivity() {\n        return this.request(\"/analytics/team/productivity\");\n    }\n    // Document endpoints\n    async getDocuments(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.document_type) query.append(\"document_type\", params.document_type);\n        if (params?.deal_id) query.append(\"deal_id\", params.deal_id);\n        if (params?.status) query.append(\"status\", params.status);\n        return this.request(`/documents?${query.toString()}`);\n    }\n    async getDocument(id) {\n        return this.request(`/documents/${id}`);\n    }\n    async createDocument(documentData) {\n        return this.request(\"/documents\", {\n            method: \"POST\",\n            body: JSON.stringify(documentData)\n        });\n    }\n    async uploadDocument(file, metadata) {\n        const formData = new FormData();\n        formData.append(\"file\", file);\n        formData.append(\"title\", metadata.title);\n        if (metadata.document_type) formData.append(\"document_type\", metadata.document_type);\n        if (metadata.deal_id) formData.append(\"deal_id\", metadata.deal_id);\n        if (metadata.client_id) formData.append(\"client_id\", metadata.client_id);\n        if (metadata.is_confidential !== undefined) formData.append(\"is_confidential\", metadata.is_confidential.toString());\n        const headers = {};\n        if (this.token) {\n            headers.Authorization = `Bearer ${this.token}`;\n        }\n        try {\n            const response = await fetch(`${this.baseUrl}/documents/upload`, {\n                method: \"POST\",\n                headers,\n                body: formData\n            });\n            if (!response.ok) {\n                const errorData = await response.json().catch(()=>({}));\n                throw new Error(errorData.detail || errorData.message || `HTTP ${response.status}`);\n            }\n            const data = await response.json();\n            return {\n                data\n            };\n        } catch (error) {\n            console.error(\"Document upload failed:\", error);\n            return {\n                error: error instanceof Error ? error.message : \"Unknown error\"\n            };\n        }\n    }\n    async updateDocument(id, documentData) {\n        return this.request(`/documents/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(documentData)\n        });\n    }\n    async deleteDocument(id) {\n        return this.request(`/documents/${id}`, {\n            method: \"DELETE\"\n        });\n    }\n    async analyzeDocument(id) {\n        return this.request(`/documents/${id}/analyze`, {\n            method: \"POST\"\n        });\n    }\n    // Financial Models endpoints\n    async getFinancialModels(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.model_type) query.append(\"model_type\", params.model_type);\n        if (params?.deal_id) query.append(\"deal_id\", params.deal_id);\n        if (params?.status) query.append(\"status\", params.status);\n        return this.request(`/financial-models?${query.toString()}`);\n    }\n    async getFinancialModel(id) {\n        return this.request(`/financial-models/${id}`);\n    }\n    async createFinancialModel(modelData) {\n        return this.request(\"/financial-models\", {\n            method: \"POST\",\n            body: JSON.stringify(modelData)\n        });\n    }\n    async updateFinancialModel(id, modelData) {\n        return this.request(`/financial-models/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(modelData)\n        });\n    }\n    async deleteFinancialModel(id) {\n        return this.request(`/financial-models/${id}`, {\n            method: \"DELETE\"\n        });\n    }\n    async createModelVersion(id, versionData) {\n        return this.request(`/financial-models/${id}/versions`, {\n            method: \"POST\",\n            body: JSON.stringify(versionData)\n        });\n    }\n    async getModelVersions(id) {\n        return this.request(`/financial-models/${id}/versions`);\n    }\n    async getModelStatistics() {\n        return this.request(\"/financial-models/statistics\");\n    }\n    // Organization endpoints\n    async getCurrentOrganization() {\n        return this.request(\"/organizations/me\");\n    }\n    async updateCurrentOrganization(orgData) {\n        return this.request(\"/organizations/me\", {\n            method: \"PUT\",\n            body: JSON.stringify(orgData)\n        });\n    }\n    async getOrganizationStats() {\n        return this.request(\"/organizations/me/stats\");\n    }\n    // Presentation endpoints\n    async getPresentations(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.status) query.append(\"status\", params.status);\n        if (params?.presentation_type) query.append(\"presentation_type\", params.presentation_type);\n        if (params?.deal_id) query.append(\"deal_id\", params.deal_id);\n        if (params?.created_by_me) query.append(\"created_by_me\", params.created_by_me.toString());\n        return this.request(`/presentations/?${query.toString()}`);\n    }\n    async getPresentation(id) {\n        return this.request(`/presentations/${id}`);\n    }\n    async createPresentation(presentationData) {\n        return this.request(\"/presentations/\", {\n            method: \"POST\",\n            body: JSON.stringify(presentationData)\n        });\n    }\n    async updatePresentation(id, presentationData) {\n        return this.request(`/presentations/${id}`, {\n            method: \"PUT\",\n            body: JSON.stringify(presentationData)\n        });\n    }\n    async deletePresentation(id) {\n        return this.request(`/presentations/${id}`, {\n            method: \"DELETE\"\n        });\n    }\n    async createPresentationVersion(id) {\n        return this.request(`/presentations/${id}/version`, {\n            method: \"POST\"\n        });\n    }\n    async getPresentationsByDeal(dealId, params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/presentations/deals/${dealId}?${query.toString()}`);\n    }\n    async getSharedPresentations(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/presentations/shared?${query.toString()}`);\n    }\n    // Slide endpoints\n    async getPresentationSlides(presentationId, params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/presentations/${presentationId}/slides?${query.toString()}`);\n    }\n    async createSlide(presentationId, slideData) {\n        return this.request(`/presentations/${presentationId}/slides/`, {\n            method: \"POST\",\n            body: JSON.stringify(slideData)\n        });\n    }\n    async getSlide(presentationId, slideId) {\n        return this.request(`/presentations/${presentationId}/slides/${slideId}`);\n    }\n    async updateSlide(presentationId, slideId, slideData) {\n        return this.request(`/presentations/${presentationId}/slides/${slideId}`, {\n            method: \"PUT\",\n            body: JSON.stringify(slideData)\n        });\n    }\n    async deleteSlide(presentationId, slideId) {\n        return this.request(`/presentations/${presentationId}/slides/${slideId}`, {\n            method: \"DELETE\"\n        });\n    }\n    async duplicateSlide(presentationId, slideId, newSlideNumber) {\n        return this.request(`/presentations/${presentationId}/slides/${slideId}/duplicate?new_slide_number=${newSlideNumber}`, {\n            method: \"POST\"\n        });\n    }\n    // Template endpoints\n    async getPresentationTemplates(params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.category) query.append(\"category\", params.category);\n        if (params?.featured_only) query.append(\"featured_only\", params.featured_only.toString());\n        if (params?.public_only) query.append(\"public_only\", params.public_only.toString());\n        return this.request(`/presentations/templates/?${query.toString()}`);\n    }\n    async createPresentationTemplate(templateData) {\n        return this.request(\"/presentations/templates/\", {\n            method: \"POST\",\n            body: JSON.stringify(templateData)\n        });\n    }\n    async getPresentationTemplate(id) {\n        return this.request(`/presentations/templates/${id}`);\n    }\n    async createPresentationFromTemplate(templateId, presentationData) {\n        return this.request(`/presentations/templates/${templateId}/use`, {\n            method: \"POST\",\n            body: JSON.stringify(presentationData)\n        });\n    }\n    // Comment endpoints\n    async getPresentationComments(presentationId, params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        if (params?.resolved_only !== undefined) query.append(\"resolved_only\", params.resolved_only.toString());\n        return this.request(`/presentations/${presentationId}/comments?${query.toString()}`);\n    }\n    async createPresentationComment(presentationId, commentData) {\n        return this.request(`/presentations/${presentationId}/comments/`, {\n            method: \"POST\",\n            body: JSON.stringify(commentData)\n        });\n    }\n    async resolvePresentationComment(presentationId, commentId) {\n        return this.request(`/presentations/${presentationId}/comments/${commentId}/resolve`, {\n            method: \"PUT\"\n        });\n    }\n    // Collaboration endpoints\n    async getPresentationActivities(presentationId, params) {\n        const query = new URLSearchParams();\n        if (params?.skip) query.append(\"skip\", params.skip.toString());\n        if (params?.limit) query.append(\"limit\", params.limit.toString());\n        return this.request(`/presentations/${presentationId}/activities?${query.toString()}`);\n    }\n    async getPresentationActiveUsers(presentationId) {\n        return this.request(`/presentations/${presentationId}/active-users`);\n    }\n}\n// Create singleton instance\nconst apiClient = new ApiClient();\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/api-client.ts\n");

/***/ }),

/***/ "(ssr)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   AuthProvider: () => (/* binding */ AuthProvider),\n/* harmony export */   useAuth: () => (/* binding */ useAuth),\n/* harmony export */   withAuth: () => (/* binding */ withAuth)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(ssr)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/ssr/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _api_client__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! ./api-client */ \"(ssr)/./lib/api-client.ts\");\n/* __next_internal_client_entry_do_not_use__ AuthProvider,useAuth,withAuth auto */ \n\n\nconst AuthContext = /*#__PURE__*/ (0,react__WEBPACK_IMPORTED_MODULE_1__.createContext)(undefined);\nfunction AuthProvider({ children }) {\n    const [user, setUser] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(null);\n    const [loading, setLoading] = (0,react__WEBPACK_IMPORTED_MODULE_1__.useState)(true);\n    (0,react__WEBPACK_IMPORTED_MODULE_1__.useEffect)(()=>{\n        // Check if user is already logged in\n        const token = localStorage.getItem(\"access_token\");\n        if (token) {\n            _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.setToken(token);\n            refreshUser();\n        } else {\n            setLoading(false);\n        }\n    }, []);\n    const refreshUser = async ()=>{\n        try {\n            const response = await _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.getCurrentUser();\n            if (response.data) {\n                setUser(response.data);\n            } else {\n                // Token is invalid, clear it\n                _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.clearToken();\n                setUser(null);\n            }\n        } catch (error) {\n            console.error(\"Failed to refresh user:\", error);\n            _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.clearToken();\n            setUser(null);\n        } finally{\n            setLoading(false);\n        }\n    };\n    const login = async (email, password)=>{\n        try {\n            const response = await _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.login(email, password);\n            if (response.data) {\n                const { access_token, refresh_token } = response.data;\n                // Store tokens\n                _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.setToken(access_token);\n                localStorage.setItem(\"refresh_token\", refresh_token);\n                // Get user data\n                await refreshUser();\n                return {\n                    success: true\n                };\n            } else {\n                return {\n                    success: false,\n                    error: response.error || \"Login failed\"\n                };\n            }\n        } catch (error) {\n            console.error(\"Login error:\", error);\n            return {\n                success: false,\n                error: error instanceof Error ? error.message : \"Login failed\"\n            };\n        }\n    };\n    const logout = ()=>{\n        _api_client__WEBPACK_IMPORTED_MODULE_2__.apiClient.logout();\n        setUser(null);\n        // Redirect to login page\n        window.location.href = \"/auth/login\";\n    };\n    const value = {\n        user,\n        loading,\n        login,\n        logout,\n        refreshUser\n    };\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(AuthContext.Provider, {\n        value: value,\n        children: children\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\lib\\\\auth-context.tsx\",\n        lineNumber: 106,\n        columnNumber: 5\n    }, this);\n}\nfunction useAuth() {\n    const context = (0,react__WEBPACK_IMPORTED_MODULE_1__.useContext)(AuthContext);\n    if (context === undefined) {\n        throw new Error(\"useAuth must be used within an AuthProvider\");\n    }\n    return context;\n}\n// Higher-order component for protecting routes\nfunction withAuth(Component) {\n    return function AuthenticatedComponent(props) {\n        const { user, loading } = useAuth();\n        if (loading) {\n            return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                className: \"flex items-center justify-center min-h-screen\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"animate-spin rounded-full h-32 w-32 border-b-2 border-blue-600\"\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\lib\\\\auth-context.tsx\",\n                    lineNumber: 128,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\lib\\\\auth-context.tsx\",\n                lineNumber: 127,\n                columnNumber: 9\n            }, this);\n        }\n        if (!user) {\n            // Redirect to login\n            if (false) {}\n            return null;\n        }\n        return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Component, {\n            ...props\n        }, void 0, false, {\n            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\lib\\\\auth-context.tsx\",\n            lineNumber: 141,\n            columnNumber: 12\n        }, this);\n    };\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(ssr)/./lib/auth-context.tsx\n");

/***/ }),

/***/ "(rsc)/./app/globals.css":
/*!*************************!*\
  !*** ./app/globals.css ***!
  \*************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (__WEBPACK_DEFAULT_EXPORT__)\n/* harmony export */ });\n/* harmony default export */ const __WEBPACK_DEFAULT_EXPORT__ = (\"37beebff4f30\");\nif (false) {}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvZ2xvYmFscy5jc3MiLCJtYXBwaW5ncyI6Ijs7OztBQUFBLGlFQUFlLGNBQWM7QUFDN0IsSUFBSSxLQUFVLEVBQUUsRUFBdUIiLCJzb3VyY2VzIjpbIndlYnBhY2s6Ly9kZWFsdmVyc2Utb3MvLi9hcHAvZ2xvYmFscy5jc3M/NmZkNiJdLCJzb3VyY2VzQ29udGVudCI6WyJleHBvcnQgZGVmYXVsdCBcIjM3YmVlYmZmNGYzMFwiXG5pZiAobW9kdWxlLmhvdCkgeyBtb2R1bGUuaG90LmFjY2VwdCgpIH1cbiJdLCJuYW1lcyI6W10sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/globals.css\n");

/***/ }),

/***/ "(rsc)/./app/layout.tsx":
/*!************************!*\
  !*** ./app/layout.tsx ***!
  \************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ RootLayout),\n/* harmony export */   metadata: () => (/* binding */ metadata)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"Inter\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-inter\"}],\"variableName\":\"inter\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"Inter\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-inter\\\"}],\\\"variableName\\\":\\\"inter\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4__);\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_display_swap_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! next/font/google/target.css?{\"path\":\"app\\\\layout.tsx\",\"import\":\"JetBrains_Mono\",\"arguments\":[{\"subsets\":[\"latin\"],\"display\":\"swap\",\"variable\":\"--font-jetbrains-mono\"}],\"variableName\":\"jetbrainsMono\"} */ \"(rsc)/./node_modules/next/font/google/target.css?{\\\"path\\\":\\\"app\\\\\\\\layout.tsx\\\",\\\"import\\\":\\\"JetBrains_Mono\\\",\\\"arguments\\\":[{\\\"subsets\\\":[\\\"latin\\\"],\\\"display\\\":\\\"swap\\\",\\\"variable\\\":\\\"--font-jetbrains-mono\\\"}],\\\"variableName\\\":\\\"jetbrainsMono\\\"}\");\n/* harmony import */ var next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_display_swap_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default = /*#__PURE__*/__webpack_require__.n(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_display_swap_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5__);\n/* harmony import */ var _globals_css__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! ./globals.css */ \"(rsc)/./app/globals.css\");\n/* harmony import */ var _components_theme_provider__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/theme-provider */ \"(rsc)/./components/theme-provider.tsx\");\n/* harmony import */ var _lib_auth_context__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/auth-context */ \"(rsc)/./lib/auth-context.tsx\");\n\n\n\n\n\n\nconst metadata = {\n    title: \"DealVerse OS - Investment Banking Platform\",\n    description: \"AI-powered investment banking platform with deal sourcing, due diligence, valuation modeling, compliance management, and presentation tools.\",\n    keywords: \"investment banking, deal sourcing, due diligence, valuation, compliance, AI\"\n};\nfunction RootLayout({ children }) {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"html\", {\n        lang: \"en\",\n        suppressHydrationWarning: true,\n        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"body\", {\n            className: `${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().className)} ${(next_font_google_target_css_path_app_layout_tsx_import_Inter_arguments_subsets_latin_display_swap_variable_font_inter_variableName_inter___WEBPACK_IMPORTED_MODULE_4___default().variable)} ${(next_font_google_target_css_path_app_layout_tsx_import_JetBrains_Mono_arguments_subsets_latin_display_swap_variable_font_jetbrains_mono_variableName_jetbrainsMono___WEBPACK_IMPORTED_MODULE_5___default().variable)} font-sans antialiased`,\n            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_lib_auth_context__WEBPACK_IMPORTED_MODULE_3__.AuthProvider, {\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_theme_provider__WEBPACK_IMPORTED_MODULE_2__.ThemeProvider, {\n                    defaultTheme: \"light\",\n                    enableSystem: true,\n                    disableTransitionOnChange: true,\n                    children: children\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\layout.tsx\",\n                    lineNumber: 35,\n                    columnNumber: 11\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\layout.tsx\",\n                lineNumber: 34,\n                columnNumber: 9\n            }, this)\n        }, void 0, false, {\n            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\layout.tsx\",\n            lineNumber: 33,\n            columnNumber: 7\n        }, this)\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\layout.tsx\",\n        lineNumber: 32,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9hcHAvbGF5b3V0LnRzeCIsIm1hcHBpbmdzIjoiOzs7Ozs7Ozs7Ozs7Ozs7QUFPTUE7QUFNQUM7QUFiZ0I7QUFHcUM7QUFDVjtBQWUxQyxNQUFNRyxXQUFxQjtJQUNoQ0MsT0FBTztJQUNQQyxhQUFhO0lBQ2JDLFVBQVU7QUFDWixFQUFDO0FBRWMsU0FBU0MsV0FBVyxFQUNqQ0MsUUFBUSxFQUdUO0lBQ0MscUJBQ0UsOERBQUNDO1FBQUtDLE1BQUs7UUFBS0Msd0JBQXdCO2tCQUN0Qyw0RUFBQ0M7WUFBS0MsV0FBVyxDQUFDLEVBQUVkLDRMQUFlLENBQUMsQ0FBQyxFQUFFQSwyTEFBYyxDQUFDLENBQUMsRUFBRUMscU5BQXNCLENBQUMsc0JBQXNCLENBQUM7c0JBQ3JHLDRFQUFDRSwyREFBWUE7MEJBQ1gsNEVBQUNELHFFQUFhQTtvQkFDWmMsY0FBYTtvQkFDYkMsWUFBWTtvQkFDWkMseUJBQXlCOzhCQUV4QlQ7Ozs7Ozs7Ozs7Ozs7Ozs7Ozs7OztBQU1iIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVhbHZlcnNlLW9zLy4vYXBwL2xheW91dC50c3g/OTk4OCJdLCJzb3VyY2VzQ29udGVudCI6WyJpbXBvcnQgJy4vZ2xvYmFscy5jc3MnXG5pbXBvcnQgdHlwZSB7IE1ldGFkYXRhIH0gZnJvbSAnbmV4dCdcbmltcG9ydCB7IEludGVyLCBKZXRCcmFpbnNfTW9ubyB9IGZyb20gJ25leHQvZm9udC9nb29nbGUnXG5pbXBvcnQgeyBUaGVtZVByb3ZpZGVyIH0gZnJvbSBcIkAvY29tcG9uZW50cy90aGVtZS1wcm92aWRlclwiXG5pbXBvcnQgeyBBdXRoUHJvdmlkZXIgfSBmcm9tIFwiQC9saWIvYXV0aC1jb250ZXh0XCJcbi8vIGltcG9ydCB7IENsZXJrUHJvdmlkZXIgfSBmcm9tICdAY2xlcmsvbmV4dGpzJyAvLyBUZW1wb3JhcmlseSBkaXNhYmxlZFxuXG5jb25zdCBpbnRlciA9IEludGVyKHtcbiAgc3Vic2V0czogWydsYXRpbiddLFxuICBkaXNwbGF5OiAnc3dhcCcsXG4gIHZhcmlhYmxlOiAnLS1mb250LWludGVyJyxcbn0pXG5cbmNvbnN0IGpldGJyYWluc01vbm8gPSBKZXRCcmFpbnNfTW9ubyh7XG4gIHN1YnNldHM6IFsnbGF0aW4nXSxcbiAgZGlzcGxheTogJ3N3YXAnLFxuICB2YXJpYWJsZTogJy0tZm9udC1qZXRicmFpbnMtbW9ubycsXG59KVxuXG5leHBvcnQgY29uc3QgbWV0YWRhdGE6IE1ldGFkYXRhID0ge1xuICB0aXRsZTogJ0RlYWxWZXJzZSBPUyAtIEludmVzdG1lbnQgQmFua2luZyBQbGF0Zm9ybScsXG4gIGRlc2NyaXB0aW9uOiAnQUktcG93ZXJlZCBpbnZlc3RtZW50IGJhbmtpbmcgcGxhdGZvcm0gd2l0aCBkZWFsIHNvdXJjaW5nLCBkdWUgZGlsaWdlbmNlLCB2YWx1YXRpb24gbW9kZWxpbmcsIGNvbXBsaWFuY2UgbWFuYWdlbWVudCwgYW5kIHByZXNlbnRhdGlvbiB0b29scy4nLFxuICBrZXl3b3JkczogJ2ludmVzdG1lbnQgYmFua2luZywgZGVhbCBzb3VyY2luZywgZHVlIGRpbGlnZW5jZSwgdmFsdWF0aW9uLCBjb21wbGlhbmNlLCBBSScsXG59XG5cbmV4cG9ydCBkZWZhdWx0IGZ1bmN0aW9uIFJvb3RMYXlvdXQoe1xuICBjaGlsZHJlbixcbn06IHtcbiAgY2hpbGRyZW46IFJlYWN0LlJlYWN0Tm9kZVxufSkge1xuICByZXR1cm4gKFxuICAgIDxodG1sIGxhbmc9XCJlblwiIHN1cHByZXNzSHlkcmF0aW9uV2FybmluZz5cbiAgICAgIDxib2R5IGNsYXNzTmFtZT17YCR7aW50ZXIuY2xhc3NOYW1lfSAke2ludGVyLnZhcmlhYmxlfSAke2pldGJyYWluc01vbm8udmFyaWFibGV9IGZvbnQtc2FucyBhbnRpYWxpYXNlZGB9PlxuICAgICAgICA8QXV0aFByb3ZpZGVyPlxuICAgICAgICAgIDxUaGVtZVByb3ZpZGVyXG4gICAgICAgICAgICBkZWZhdWx0VGhlbWU9XCJsaWdodFwiXG4gICAgICAgICAgICBlbmFibGVTeXN0ZW1cbiAgICAgICAgICAgIGRpc2FibGVUcmFuc2l0aW9uT25DaGFuZ2VcbiAgICAgICAgICA+XG4gICAgICAgICAgICB7Y2hpbGRyZW59XG4gICAgICAgICAgPC9UaGVtZVByb3ZpZGVyPlxuICAgICAgICA8L0F1dGhQcm92aWRlcj5cbiAgICAgIDwvYm9keT5cbiAgICA8L2h0bWw+XG4gIClcbn0iXSwibmFtZXMiOlsiaW50ZXIiLCJqZXRicmFpbnNNb25vIiwiVGhlbWVQcm92aWRlciIsIkF1dGhQcm92aWRlciIsIm1ldGFkYXRhIiwidGl0bGUiLCJkZXNjcmlwdGlvbiIsImtleXdvcmRzIiwiUm9vdExheW91dCIsImNoaWxkcmVuIiwiaHRtbCIsImxhbmciLCJzdXBwcmVzc0h5ZHJhdGlvbldhcm5pbmciLCJib2R5IiwiY2xhc3NOYW1lIiwidmFyaWFibGUiLCJkZWZhdWx0VGhlbWUiLCJlbmFibGVTeXN0ZW0iLCJkaXNhYmxlVHJhbnNpdGlvbk9uQ2hhbmdlIl0sInNvdXJjZVJvb3QiOiIifQ==\n//# sourceURL=webpack-internal:///(rsc)/./app/layout.tsx\n");

/***/ }),

/***/ "(rsc)/./app/page.tsx":
/*!**********************!*\
  !*** ./app/page.tsx ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   \"default\": () => (/* binding */ Home)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var next_link__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! next/link */ \"(rsc)/./node_modules/next/dist/api/link.js\");\n/* harmony import */ var _components_ui_button__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/components/ui/button */ \"(rsc)/./components/ui/button.tsx\");\n/* harmony import */ var _components_ui_card__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/components/ui/card */ \"(rsc)/./components/ui/card.tsx\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calculator,CheckCircle,FileText,Presentation,Search,Shield!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/arrow-right.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calculator,CheckCircle,FileText,Presentation,Search,Shield!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/search.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calculator,CheckCircle,FileText,Presentation,Search,Shield!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/check-circle.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calculator,CheckCircle,FileText,Presentation,Search,Shield!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/file-text.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calculator,CheckCircle,FileText,Presentation,Search,Shield!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/calculator.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calculator,CheckCircle,FileText,Presentation,Search,Shield!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/shield.js\");\n/* harmony import */ var _barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__ = __webpack_require__(/*! __barrel_optimize__?names=ArrowRight,Calculator,CheckCircle,FileText,Presentation,Search,Shield!=!lucide-react */ \"(rsc)/./node_modules/lucide-react/dist/esm/icons/presentation.js\");\n\n\n\n\n\nfunction Home() {\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        className: \"min-h-screen bg-gradient-to-br from-dealverse-navy via-dealverse-navy to-dealverse-dark-gray\",\n        children: [\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"nav\", {\n                className: \"border-b border-dealverse-navy/20 bg-dealverse-navy/95 backdrop-blur-sm\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"flex justify-between items-center h-16\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-3\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                        className: \"w-8 h-8 bg-gradient-to-br from-dealverse-blue to-dealverse-green rounded-lg flex items-center justify-center\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                            className: \"text-white font-bold text-sm\",\n                                            children: \"DV\"\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                            lineNumber: 29,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                        lineNumber: 28,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                        className: \"font-bold text-xl text-white\",\n                                        children: \"DealVerse OS\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                        lineNumber: 31,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                lineNumber: 27,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex items-center space-x-4\",\n                                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                    href: \"/dashboard\",\n                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        className: \"bg-dealverse-blue hover:bg-dealverse-blue/90 text-white\",\n                                        children: [\n                                            \"Launch Dashboard\",\n                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                className: \"ml-2 h-4 w-4\"\n                                            }, void 0, false, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                lineNumber: 37,\n                                                columnNumber: 19\n                                            }, this)\n                                        ]\n                                    }, void 0, true, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                        lineNumber: 35,\n                                        columnNumber: 17\n                                    }, this)\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                    lineNumber: 34,\n                                    columnNumber: 15\n                                }, this)\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                lineNumber: 33,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                        lineNumber: 26,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                    lineNumber: 25,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                lineNumber: 24,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"relative py-20 px-4 sm:px-6 lg:px-8\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                        className: \"text-center\",\n                        children: [\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h1\", {\n                                className: \"text-4xl md:text-6xl font-bold text-white mb-6 leading-tight\",\n                                children: [\n                                    \"The Future of\",\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"span\", {\n                                        className: \"bg-gradient-to-r from-dealverse-blue to-dealverse-green bg-clip-text text-transparent block\",\n                                        children: \"Investment Banking\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                        lineNumber: 51,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                lineNumber: 49,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                className: \"text-xl text-dealverse-light-gray mb-8 max-w-3xl mx-auto leading-relaxed\",\n                                children: \"DealVerse OS revolutionizes investment banking with AI-powered deal sourcing, automated due diligence, advanced valuation modeling, and comprehensive compliance management.\"\n                            }, void 0, false, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                lineNumber: 55,\n                                columnNumber: 13\n                            }, this),\n                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                className: \"flex flex-col sm:flex-row gap-4 justify-center\",\n                                children: [\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(next_link__WEBPACK_IMPORTED_MODULE_1__[\"default\"], {\n                                        href: \"/dashboard\",\n                                        children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                            size: \"lg\",\n                                            className: \"bg-dealverse-blue hover:bg-dealverse-blue/90 text-white px-8 py-4 text-lg\",\n                                            children: [\n                                                \"Start Free Trial\",\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_4__[\"default\"], {\n                                                    className: \"ml-2 h-5 w-5\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                    lineNumber: 63,\n                                                    columnNumber: 19\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                            lineNumber: 61,\n                                            columnNumber: 17\n                                        }, this)\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                        lineNumber: 60,\n                                        columnNumber: 15\n                                    }, this),\n                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_button__WEBPACK_IMPORTED_MODULE_2__.Button, {\n                                        size: \"lg\",\n                                        variant: \"outline\",\n                                        className: \"border-dealverse-blue text-dealverse-blue hover:bg-dealverse-blue/10 px-8 py-4 text-lg\",\n                                        children: \"Watch Demo\"\n                                    }, void 0, false, {\n                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                        lineNumber: 66,\n                                        columnNumber: 15\n                                    }, this)\n                                ]\n                            }, void 0, true, {\n                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                lineNumber: 59,\n                                columnNumber: 13\n                            }, this)\n                        ]\n                    }, void 0, true, {\n                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                        lineNumber: 48,\n                        columnNumber: 11\n                    }, this)\n                }, void 0, false, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                    lineNumber: 47,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                lineNumber: 46,\n                columnNumber: 7\n            }, this),\n            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"section\", {\n                className: \"py-20 px-4 sm:px-6 lg:px-8 bg-dealverse-dark-gray/30\",\n                children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                    className: \"max-w-7xl mx-auto\",\n                    children: [\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"text-center mb-16\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h2\", {\n                                    className: \"text-3xl md:text-4xl font-bold text-white mb-4\",\n                                    children: \"Five Integrated Modules\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                    lineNumber: 78,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n                                    className: \"text-xl text-dealverse-light-gray max-w-2xl mx-auto\",\n                                    children: \"Everything you need for modern investment banking, powered by artificial intelligence\"\n                                }, void 0, false, {\n                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                    lineNumber: 81,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                            lineNumber: 77,\n                            columnNumber: 11\n                        }, this),\n                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                            className: \"grid md:grid-cols-2 lg:grid-cols-3 gap-8\",\n                            children: [\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-dealverse-navy/50 border-dealverse-blue/20 hover:border-dealverse-blue/40 transition-all duration-300 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-dealverse-blue/20 to-dealverse-blue/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_5__[\"default\"], {\n                                                        className: \"h-6 w-6 text-dealverse-blue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 91,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                    lineNumber: 90,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl text-white\",\n                                                    children: \"Prospect AI\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                    lineNumber: 93,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    className: \"text-dealverse-light-gray\",\n                                                    children: \"AI-powered deal sourcing and opportunity management with market intelligence\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                    lineNumber: 94,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                            lineNumber: 89,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-sm text-dealverse-light-gray\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 text-dealverse-green mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                                lineNumber: 101,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Automated prospect identification\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 100,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 text-dealverse-green mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                                lineNumber: 105,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"AI confidence scoring\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 104,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 text-dealverse-green mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                                lineNumber: 109,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Market trend analysis\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 108,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                lineNumber: 99,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                            lineNumber: 98,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                    lineNumber: 88,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-dealverse-navy/50 border-dealverse-blue/20 hover:border-dealverse-blue/40 transition-all duration-300 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-dealverse-blue/20 to-dealverse-blue/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_7__[\"default\"], {\n                                                        className: \"h-6 w-6 text-dealverse-blue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 120,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                    lineNumber: 119,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl text-white\",\n                                                    children: \"Diligence Navigator\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                    lineNumber: 122,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    className: \"text-dealverse-light-gray\",\n                                                    children: \"Streamlined due diligence with automated document analysis and risk assessment\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                    lineNumber: 123,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                            lineNumber: 118,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-sm text-dealverse-light-gray\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 text-dealverse-green mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                                lineNumber: 130,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Document tree organization\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 129,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 text-dealverse-green mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                                lineNumber: 134,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"AI risk flagging\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 133,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 text-dealverse-green mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                                lineNumber: 138,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Missing document alerts\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 137,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                lineNumber: 128,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                            lineNumber: 127,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                    lineNumber: 117,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-dealverse-navy/50 border-dealverse-blue/20 hover:border-dealverse-blue/40 transition-all duration-300 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-dealverse-blue/20 to-dealverse-blue/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_8__[\"default\"], {\n                                                        className: \"h-6 w-6 text-dealverse-blue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 149,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                    lineNumber: 148,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl text-white\",\n                                                    children: \"Valuation & Modeling Hub\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                    lineNumber: 151,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    className: \"text-dealverse-light-gray\",\n                                                    children: \"Advanced financial modeling with collaborative features and scenario analysis\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                    lineNumber: 152,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                            lineNumber: 147,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-sm text-dealverse-light-gray\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 text-dealverse-green mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                                lineNumber: 159,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Collaborative modeling\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 158,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 text-dealverse-green mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                                lineNumber: 163,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Scenario comparison\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 162,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 text-dealverse-green mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                                lineNumber: 167,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Automated visualizations\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 166,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                lineNumber: 157,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                            lineNumber: 156,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                    lineNumber: 146,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-dealverse-navy/50 border-dealverse-blue/20 hover:border-dealverse-blue/40 transition-all duration-300 group\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-dealverse-blue/20 to-dealverse-blue/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_9__[\"default\"], {\n                                                        className: \"h-6 w-6 text-dealverse-blue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 178,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                    lineNumber: 177,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl text-white\",\n                                                    children: \"Compliance Guardian\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                    lineNumber: 180,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    className: \"text-dealverse-light-gray\",\n                                                    children: \"Comprehensive compliance management with automated monitoring and reporting\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                    lineNumber: 181,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                            lineNumber: 176,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-sm text-dealverse-light-gray\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 text-dealverse-green mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                                lineNumber: 188,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Traffic light system\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 187,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 text-dealverse-green mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                                lineNumber: 192,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Audit trail timeline\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 191,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 text-dealverse-green mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                                lineNumber: 196,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Regulatory updates\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 195,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                lineNumber: 186,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                            lineNumber: 185,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                    lineNumber: 175,\n                                    columnNumber: 13\n                                }, this),\n                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.Card, {\n                                    className: \"bg-dealverse-navy/50 border-dealverse-blue/20 hover:border-dealverse-blue/40 transition-all duration-300 group md:col-span-2 lg:col-span-1\",\n                                    children: [\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardHeader, {\n                                            children: [\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n                                                    className: \"w-12 h-12 bg-gradient-to-br from-dealverse-blue/20 to-dealverse-blue/30 rounded-lg flex items-center justify-center mb-4 group-hover:scale-110 transition-transform duration-300\",\n                                                    children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_10__[\"default\"], {\n                                                        className: \"h-6 w-6 text-dealverse-blue\"\n                                                    }, void 0, false, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 207,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                    lineNumber: 206,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardTitle, {\n                                                    className: \"text-xl text-white\",\n                                                    children: \"PitchCraft Suite\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                    lineNumber: 209,\n                                                    columnNumber: 17\n                                                }, this),\n                                                /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardDescription, {\n                                                    className: \"text-dealverse-light-gray\",\n                                                    children: \"Professional presentation builder with templates and real-time collaboration\"\n                                                }, void 0, false, {\n                                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                    lineNumber: 210,\n                                                    columnNumber: 17\n                                                }, this)\n                                            ]\n                                        }, void 0, true, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                            lineNumber: 205,\n                                            columnNumber: 15\n                                        }, this),\n                                        /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_components_ui_card__WEBPACK_IMPORTED_MODULE_3__.CardContent, {\n                                            children: /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"ul\", {\n                                                className: \"space-y-2 text-sm text-dealverse-light-gray\",\n                                                children: [\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 text-dealverse-green mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                                lineNumber: 217,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Drag-and-drop builder\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 216,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 text-dealverse-green mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                                lineNumber: 221,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Template gallery\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 220,\n                                                        columnNumber: 19\n                                                    }, this),\n                                                    /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"li\", {\n                                                        className: \"flex items-center\",\n                                                        children: [\n                                                            /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(_barrel_optimize_names_ArrowRight_Calculator_CheckCircle_FileText_Presentation_Search_Shield_lucide_react__WEBPACK_IMPORTED_MODULE_6__[\"default\"], {\n                                                                className: \"h-4 w-4 text-dealverse-green mr-2\"\n                                                            }, void 0, false, {\n                                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                                lineNumber: 225,\n                                                                columnNumber: 21\n                                                            }, this),\n                                                            \"Real-time collaboration\"\n                                                        ]\n                                                    }, void 0, true, {\n                                                        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                        lineNumber: 224,\n                                                        columnNumber: 19\n                                                    }, this)\n                                                ]\n                                            }, void 0, true, {\n                                                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                                lineNumber: 215,\n                                                columnNumber: 17\n                                            }, this)\n                                        }, void 0, false, {\n                                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                            lineNumber: 214,\n                                            columnNumber: 15\n                                        }, this)\n                                    ]\n                                }, void 0, true, {\n                                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                                    lineNumber: 204,\n                                    columnNumber: 13\n                                }, this)\n                            ]\n                        }, void 0, true, {\n                            fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                            lineNumber: 86,\n                            columnNumber: 11\n                        }, this)\n                    ]\n                }, void 0, true, {\n                    fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                    lineNumber: 76,\n                    columnNumber: 9\n                }, this)\n            }, void 0, false, {\n                fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n                lineNumber: 75,\n                columnNumber: 7\n            }, this)\n        ]\n    }, void 0, true, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\app\\\\page.tsx\",\n        lineNumber: 22,\n        columnNumber: 5\n    }, this);\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./app/page.tsx\n");

/***/ }),

/***/ "(rsc)/./components/theme-provider.tsx":
/*!***************************************!*\
  !*** ./components/theme-provider.tsx ***!
  \***************************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   ThemeProvider: () => (/* binding */ e0)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Tempo\Micro Saas Research\Researches on Micro SAAS Opportunities\DealVerse OS\DealVerse OS App\components\theme-provider.tsx#ThemeProvider`);


/***/ }),

/***/ "(rsc)/./components/ui/button.tsx":
/*!**********************************!*\
  !*** ./components/ui/button.tsx ***!
  \**********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Button: () => (/* binding */ Button),\n/* harmony export */   buttonVariants: () => (/* binding */ buttonVariants)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__ = __webpack_require__(/*! @radix-ui/react-slot */ \"(rsc)/./node_modules/@radix-ui/react-slot/dist/index.mjs\");\n/* harmony import */ var class_variance_authority__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! class-variance-authority */ \"(rsc)/./node_modules/class-variance-authority/dist/index.mjs\");\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_3__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./lib/utils.ts\");\n\n\n\n\n\nconst buttonVariants = (0,class_variance_authority__WEBPACK_IMPORTED_MODULE_2__.cva)(\"inline-flex items-center justify-center whitespace-nowrap rounded-lg text-sm font-medium ring-offset-background transition-all duration-300 ease-in-out focus-visible:outline-none focus-visible:ring-2 focus-visible:ring-dealverse-blue focus-visible:ring-offset-2 disabled:pointer-events-none disabled:opacity-50 active:scale-95\", {\n    variants: {\n        variant: {\n            default: \"bg-dealverse-blue text-white hover:bg-dealverse-blue/90 hover:shadow-lg hover:shadow-dealverse-blue/25 hover:scale-105\",\n            destructive: \"bg-dealverse-coral text-white hover:bg-dealverse-coral/90 hover:shadow-lg hover:shadow-dealverse-coral/25\",\n            outline: \"border-2 border-dealverse-blue bg-transparent text-dealverse-blue hover:bg-dealverse-blue hover:text-white hover:shadow-lg\",\n            secondary: \"bg-dealverse-navy text-white hover:bg-dealverse-navy/90 hover:shadow-lg\",\n            ghost: \"hover:bg-dealverse-blue/10 hover:text-dealverse-blue\",\n            link: \"text-dealverse-blue underline-offset-4 hover:underline hover:text-dealverse-blue/80\",\n            success: \"bg-dealverse-green text-white hover:bg-dealverse-green/90 hover:shadow-lg hover:shadow-dealverse-green/25\",\n            warning: \"bg-dealverse-amber text-white hover:bg-dealverse-amber/90 hover:shadow-lg hover:shadow-dealverse-amber/25\"\n        },\n        size: {\n            default: \"h-10 px-4 py-2\",\n            sm: \"h-9 rounded-lg px-3\",\n            lg: \"h-12 rounded-lg px-8 text-base\",\n            icon: \"h-10 w-10\"\n        }\n    },\n    defaultVariants: {\n        variant: \"default\",\n        size: \"default\"\n    }\n});\nconst Button = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, variant, size, asChild = false, ...props }, ref)=>{\n    const Comp = asChild ? _radix_ui_react_slot__WEBPACK_IMPORTED_MODULE_4__.Slot : \"button\";\n    return /*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(Comp, {\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_3__.cn)(buttonVariants({\n            variant,\n            size,\n            className\n        })),\n        ref: ref,\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\button.tsx\",\n        lineNumber: 48,\n        columnNumber: 7\n    }, undefined);\n});\nButton.displayName = \"Button\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/ui/button.tsx\n");

/***/ }),

/***/ "(rsc)/./components/ui/card.tsx":
/*!********************************!*\
  !*** ./components/ui/card.tsx ***!
  \********************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   Card: () => (/* binding */ Card),\n/* harmony export */   CardContent: () => (/* binding */ CardContent),\n/* harmony export */   CardDescription: () => (/* binding */ CardDescription),\n/* harmony export */   CardFooter: () => (/* binding */ CardFooter),\n/* harmony export */   CardHeader: () => (/* binding */ CardHeader),\n/* harmony export */   CardTitle: () => (/* binding */ CardTitle)\n/* harmony export */ });\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! react/jsx-dev-runtime */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react-jsx-dev-runtime.js\");\n/* harmony import */ var react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0___default = /*#__PURE__*/__webpack_require__.n(react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__);\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! react */ \"(rsc)/./node_modules/next/dist/server/future/route-modules/app-page/vendored/rsc/react.js\");\n/* harmony import */ var react__WEBPACK_IMPORTED_MODULE_1___default = /*#__PURE__*/__webpack_require__.n(react__WEBPACK_IMPORTED_MODULE_1__);\n/* harmony import */ var _lib_utils__WEBPACK_IMPORTED_MODULE_2__ = __webpack_require__(/*! @/lib/utils */ \"(rsc)/./lib/utils.ts\");\n\n\n\nconst Card = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"rounded-xl border bg-card text-card-foreground shadow-sm transition-all duration-300 ease-in-out hover:shadow-md\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 9,\n        columnNumber: 3\n    }, undefined));\nCard.displayName = \"Card\";\nconst CardHeader = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex flex-col space-y-1.5 p-6\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 24,\n        columnNumber: 3\n    }, undefined));\nCardHeader.displayName = \"CardHeader\";\nconst CardTitle = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"h3\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-2xl font-semibold leading-none tracking-tight\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 36,\n        columnNumber: 3\n    }, undefined));\nCardTitle.displayName = \"CardTitle\";\nconst CardDescription = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"p\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"text-sm text-muted-foreground\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 51,\n        columnNumber: 3\n    }, undefined));\nCardDescription.displayName = \"CardDescription\";\nconst CardContent = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 63,\n        columnNumber: 3\n    }, undefined));\nCardContent.displayName = \"CardContent\";\nconst CardFooter = /*#__PURE__*/ react__WEBPACK_IMPORTED_MODULE_1__.forwardRef(({ className, ...props }, ref)=>/*#__PURE__*/ (0,react_jsx_dev_runtime__WEBPACK_IMPORTED_MODULE_0__.jsxDEV)(\"div\", {\n        ref: ref,\n        className: (0,_lib_utils__WEBPACK_IMPORTED_MODULE_2__.cn)(\"flex items-center p-6 pt-0\", className),\n        ...props\n    }, void 0, false, {\n        fileName: \"E:\\\\Tempo\\\\Micro Saas Research\\\\Researches on Micro SAAS Opportunities\\\\DealVerse OS\\\\DealVerse OS App\\\\components\\\\ui\\\\card.tsx\",\n        lineNumber: 71,\n        columnNumber: 3\n    }, undefined));\nCardFooter.displayName = \"CardFooter\";\n\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,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\n//# sourceURL=webpack-internal:///(rsc)/./components/ui/card.tsx\n");

/***/ }),

/***/ "(rsc)/./lib/auth-context.tsx":
/*!******************************!*\
  !*** ./lib/auth-context.tsx ***!
  \******************************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
__webpack_require__.r(__webpack_exports__);
/* harmony export */ __webpack_require__.d(__webpack_exports__, {
/* harmony export */   AuthProvider: () => (/* binding */ e0),
/* harmony export */   useAuth: () => (/* binding */ e1),
/* harmony export */   withAuth: () => (/* binding */ e2)
/* harmony export */ });
/* harmony import */ var next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! next/dist/build/webpack/loaders/next-flight-loader/module-proxy */ "(rsc)/./node_modules/next/dist/build/webpack/loaders/next-flight-loader/module-proxy.js");


const e0 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Tempo\Micro Saas Research\Researches on Micro SAAS Opportunities\DealVerse OS\DealVerse OS App\lib\auth-context.tsx#AuthProvider`);

const e1 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Tempo\Micro Saas Research\Researches on Micro SAAS Opportunities\DealVerse OS\DealVerse OS App\lib\auth-context.tsx#useAuth`);

const e2 = (0,next_dist_build_webpack_loaders_next_flight_loader_module_proxy__WEBPACK_IMPORTED_MODULE_0__.createProxy)(String.raw`E:\Tempo\Micro Saas Research\Researches on Micro SAAS Opportunities\DealVerse OS\DealVerse OS App\lib\auth-context.tsx#withAuth`);


/***/ }),

/***/ "(rsc)/./lib/utils.ts":
/*!**********************!*\
  !*** ./lib/utils.ts ***!
  \**********************/
/***/ ((__unused_webpack_module, __webpack_exports__, __webpack_require__) => {

"use strict";
eval("__webpack_require__.r(__webpack_exports__);\n/* harmony export */ __webpack_require__.d(__webpack_exports__, {\n/* harmony export */   cn: () => (/* binding */ cn)\n/* harmony export */ });\n/* harmony import */ var clsx__WEBPACK_IMPORTED_MODULE_0__ = __webpack_require__(/*! clsx */ \"(rsc)/./node_modules/clsx/dist/clsx.mjs\");\n/* harmony import */ var tailwind_merge__WEBPACK_IMPORTED_MODULE_1__ = __webpack_require__(/*! tailwind-merge */ \"(rsc)/./node_modules/tailwind-merge/dist/bundle-mjs.mjs\");\n\n\nfunction cn(...inputs) {\n    return (0,tailwind_merge__WEBPACK_IMPORTED_MODULE_1__.twMerge)((0,clsx__WEBPACK_IMPORTED_MODULE_0__.clsx)(inputs));\n}\n//# sourceURL=[module]\n//# sourceMappingURL=data:application/json;charset=utf-8;base64,eyJ2ZXJzaW9uIjozLCJmaWxlIjoiKHJzYykvLi9saWIvdXRpbHMudHMiLCJtYXBwaW5ncyI6Ijs7Ozs7O0FBQTRDO0FBQ0o7QUFFakMsU0FBU0UsR0FBRyxHQUFHQyxNQUFvQjtJQUN4QyxPQUFPRix1REFBT0EsQ0FBQ0QsMENBQUlBLENBQUNHO0FBQ3RCIiwic291cmNlcyI6WyJ3ZWJwYWNrOi8vZGVhbHZlcnNlLW9zLy4vbGliL3V0aWxzLnRzP2Y3NDUiXSwic291cmNlc0NvbnRlbnQiOlsiaW1wb3J0IHsgdHlwZSBDbGFzc1ZhbHVlLCBjbHN4IH0gZnJvbSBcImNsc3hcIlxuaW1wb3J0IHsgdHdNZXJnZSB9IGZyb20gXCJ0YWlsd2luZC1tZXJnZVwiXG5cbmV4cG9ydCBmdW5jdGlvbiBjbiguLi5pbnB1dHM6IENsYXNzVmFsdWVbXSkge1xuICByZXR1cm4gdHdNZXJnZShjbHN4KGlucHV0cykpXG59XG4iXSwibmFtZXMiOlsiY2xzeCIsInR3TWVyZ2UiLCJjbiIsImlucHV0cyJdLCJzb3VyY2VSb290IjoiIn0=\n//# sourceURL=webpack-internal:///(rsc)/./lib/utils.ts\n");

/***/ })

};
;

// load runtime
var __webpack_require__ = require("../webpack-runtime.js");
__webpack_require__.C(exports);
var __webpack_exec__ = (moduleId) => (__webpack_require__(__webpack_require__.s = moduleId))
var __webpack_exports__ = __webpack_require__.X(0, ["vendor-chunks/next","vendor-chunks/@radix-ui","vendor-chunks/tailwind-merge","vendor-chunks/lucide-react","vendor-chunks/class-variance-authority","vendor-chunks/next-themes","vendor-chunks/@swc","vendor-chunks/clsx"], () => (__webpack_exec__("(rsc)/./node_modules/next/dist/build/webpack/loaders/next-app-loader.js?name=app%2Fpage&page=%2Fpage&appPaths=%2Fpage&pagePath=private-next-app-dir%2Fpage.tsx&appDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App%5Capp&pageExtensions=tsx&pageExtensions=ts&pageExtensions=jsx&pageExtensions=js&rootDir=E%3A%5CTempo%5CMicro%20Saas%20Research%5CResearches%20on%20Micro%20SAAS%20Opportunities%5CDealVerse%20OS%5CDealVerse%20OS%20App&isDev=true&tsconfigPath=tsconfig.json&basePath=&assetPrefix=&nextConfigOutput=&preferredRegion=&middlewareConfig=e30%3D!")));
module.exports = __webpack_exports__;

})();